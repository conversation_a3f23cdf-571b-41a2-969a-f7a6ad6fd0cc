# 双维度组织架构技术设计文档

## 1. 技术架构概述

### 1.1 设计原则
- **兼容性优先**：保持现有部门权限体系不变
- **模块化设计**：项目维度作为独立模块，可插拔
- **性能优化**：合理使用缓存，优化权限查询性能
- **扩展性**：支持未来更多组织维度的扩展

### 1.2 架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    双维度组织架构系统                          │
├─────────────────────────────────────────────────────────────┤
│  前端层 (Vue3 + TypeScript)                                 │
│  ├── 组织形态切换器                                          │
│  ├── 动态菜单系统                                            │
│  ├── 部门维度界面                                            │
│  └── 项目维度界面                                            │
├─────────────────────────────────────────────────────────────┤
│  服务层 (Spring Boot)                                       │
│  ├── 组织形态管理服务                                        │
│  ├── 双维度权限服务                                          │
│  ├── 项目管理服务                                            │
│  └── 用户组织关系服务                                        │
├─────────────────────────────────────────────────────────────┤
│  数据层 (MySQL + Redis)                                     │
│  ├── 用户组织关系表                                          │
│  ├── 项目信息表                                              │
│  ├── 用户当前模式表                                          │
│  └── 权限缓存                                                │
└─────────────────────────────────────────────────────────────┘
```

## 2. 数据库设计

### 2.1 核心表结构

#### 2.1.1 用户组织关系表 (user_organization)
```sql
CREATE TABLE `user_organization` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `organization_type` varchar(20) NOT NULL COMMENT '组织类型：DEPARTMENT/PROJECT',
  `organization_id` bigint NOT NULL COMMENT '组织ID（部门ID或项目ID）',
  `role_code` varchar(50) NOT NULL COMMENT '角色代码',
  `is_primary` tinyint DEFAULT '0' COMMENT '是否主要组织：0-否 1-是',
  `join_time` datetime NOT NULL COMMENT '加入时间',
  `expire_time` datetime DEFAULT NULL COMMENT '权限到期时间',
  `status` tinyint DEFAULT '1' COMMENT '状态：1-正常 2-暂停 3-已移除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_org` (`user_id`, `organization_type`, `organization_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_org_type_id` (`organization_type`, `organization_id`),
  KEY `idx_role_code` (`role_code`)
) COMMENT='用户组织关系表';
```

#### 2.1.2 项目信息表 (project_info)
```sql
CREATE TABLE `project_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `project_name` varchar(200) NOT NULL COMMENT '项目名称',
  `project_code` varchar(100) NOT NULL COMMENT '项目编码',
  `description` text COMMENT '项目描述',
  `owner_id` bigint NOT NULL COMMENT '项目负责人ID',
  `status` tinyint DEFAULT '1' COMMENT '项目状态：1-进行中 2-已完成 3-已暂停 4-已取消',
  `priority` tinyint DEFAULT '3' COMMENT '项目优先级：1-低 2-普通 3-中等 4-高 5-紧急',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `tags` json DEFAULT NULL COMMENT '项目标签',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_code` (`project_code`),
  KEY `idx_owner_id` (`owner_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`)
) COMMENT='项目信息表';
```

#### 2.1.3 用户当前组织形态表 (user_current_mode)
```sql
CREATE TABLE `user_current_mode` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `current_mode` varchar(20) NOT NULL COMMENT '当前组织形态：DEPARTMENT/PROJECT',
  `last_switch_time` datetime NOT NULL COMMENT '最后切换时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`)
) COMMENT='用户当前组织形态表';
```

### 2.2 数据关系图
```
用户表 (base_sys_user)
    ↓ 1:N
用户组织关系表 (user_organization)
    ↓ N:1
组织表 (base_sys_dept / project_info)

用户表 (base_sys_user)
    ↓ 1:1
用户当前模式表 (user_current_mode)
```

## 3. 后端技术实现

### 3.1 核心枚举定义

#### 3.1.1 组织形态枚举
```java
public enum OrganizationModeEnum {
    DEPARTMENT("DEPARTMENT", "部门维度"),
    PROJECT("PROJECT", "项目维度");
    
    private final String code;
    private final String name;
    
    OrganizationModeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    // getter methods...
}
```

#### 3.1.2 全局项目角色枚举
```java
public enum GlobalProjectRoleEnum {
    PROJECT_OWNER("PROJECT_OWNER", "项目负责人", 4),
    PROJECT_ADMIN("PROJECT_ADMIN", "项目管理员", 3),
    PROJECT_MEMBER("PROJECT_MEMBER", "项目成员", 2),
    PROJECT_VIEWER("PROJECT_VIEWER", "项目观察者", 1);
    
    private final String code;
    private final String name;
    private final Integer level;
    
    // constructor and getter methods...
}
```

### 3.2 核心服务实现

#### 3.2.1 组织形态管理服务
```java
@Service
@RequiredArgsConstructor
public class OrganizationModeServiceImpl implements OrganizationModeService {
    
    private final UserCurrentModeService userCurrentModeService;
    private final UserOrganizationService userOrganizationService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public String getCurrentMode(Long userId) {
        String cacheKey = "user:current:mode:" + userId;
        String cachedMode = (String) redisTemplate.opsForValue().get(cacheKey);
        
        if (cachedMode != null) {
            return cachedMode;
        }
        
        UserCurrentModeEntity currentMode = userCurrentModeService.getByUserId(userId);
        String mode = currentMode != null ? currentMode.getCurrentMode() : 
                     OrganizationModeEnum.DEPARTMENT.getCode();
        
        redisTemplate.opsForValue().set(cacheKey, mode, 30, TimeUnit.MINUTES);
        return mode;
    }
    
    @Override
    @Transactional
    public void switchMode(Long userId, String targetMode) {
        validateModeSwitch(userId, targetMode);
        
        UserCurrentModeEntity currentMode = userCurrentModeService.getByUserId(userId);
        if (currentMode == null) {
            currentMode = new UserCurrentModeEntity();
            currentMode.setUserId(userId);
        }
        currentMode.setCurrentMode(targetMode);
        currentMode.setLastSwitchTime(new Date());
        userCurrentModeService.saveOrUpdate(currentMode);
        
        clearUserCache(userId);
    }
    
    private void validateModeSwitch(Long userId, String targetMode) {
        if (!OrganizationModeEnum.isValid(targetMode)) {
            throw new CoolException("无效的组织形态");
        }
        
        if (!canSwitchToMode(userId, targetMode)) {
            throw new CoolException("您没有权限切换到该组织形态");
        }
    }
    
    private void clearUserCache(Long userId) {
        String[] cacheKeys = {
            "user:current:mode:" + userId,
            "user:accessible:projects:" + userId,
            "user:permissions:" + userId
        };
        redisTemplate.delete(Arrays.asList(cacheKeys));
    }
}
```

#### 3.2.2 双维度权限服务
```java
@Service
@RequiredArgsConstructor
public class DualDimensionPermissionServiceImpl implements DualDimensionPermissionService {
    
    private final OrganizationModeService organizationModeService;
    private final DepartmentPermissionService departmentPermissionService;
    private final ProjectPermissionService projectPermissionService;
    private final GlobalProjectRoleConfig roleConfig;
    
    @Override
    public boolean hasPermission(Long userId, String permission, Map<String, Object> context) {
        // 系统管理员拥有所有权限
        if (isSystemAdmin(userId)) {
            return true;
        }
        
        String currentMode = organizationModeService.getCurrentMode(userId);
        
        if (OrganizationModeEnum.DEPARTMENT.getCode().equals(currentMode)) {
            return departmentPermissionService.hasPermission(userId, permission, context);
        } else if (OrganizationModeEnum.PROJECT.getCode().equals(currentMode)) {
            return hasProjectPermission(userId, permission, context);
        }
        
        return false;
    }
    
    private boolean hasProjectPermission(Long userId, String permission, Map<String, Object> context) {
        Long projectId = (Long) context.get("projectId");
        if (projectId == null) {
            return hasGlobalProjectPermission(userId, permission);
        }
        
        String userRole = getUserProjectRole(userId, projectId);
        if (userRole == null) {
            return false;
        }
        
        List<String> rolePermissions = roleConfig.getRolePermissions(userRole);
        return rolePermissions.contains(permission);
    }
    
    @Override
    @Cacheable(value = "user:menus", key = "#userId")
    public List<String> getUserMenus(Long userId) {
        String currentMode = organizationModeService.getCurrentMode(userId);
        
        if (OrganizationModeEnum.DEPARTMENT.getCode().equals(currentMode)) {
            return getDepartmentModeMenus(userId);
        } else if (OrganizationModeEnum.PROJECT.getCode().equals(currentMode)) {
            return getProjectModeMenus(userId);
        }
        
        return Collections.emptyList();
    }
    
    private List<String> getProjectModeMenus(Long userId) {
        String highestRole = getHighestProjectRole(userId);
        return roleConfig.getRoleMenus(highestRole);
    }
}
```

### 3.3 全局项目角色配置

#### 3.3.1 角色权限配置类
```java
@Component
@ConfigurationProperties(prefix = "cool.project.roles")
public class GlobalProjectRoleConfig {
    
    private static final Map<String, List<String>> ROLE_PERMISSIONS = new HashMap<>();
    private static final Map<String, List<String>> ROLE_MENUS = new HashMap<>();
    
    static {
        initRolePermissions();
        initRoleMenus();
    }
    
    private static void initRolePermissions() {
        ROLE_PERMISSIONS.put("PROJECT_OWNER", Arrays.asList(
            "project:view", "project:edit", "project:delete", "project:archive",
            "project:member:view", "project:member:add", "project:member:remove", 
            "project:member:role:change", "project:task:view", "project:task:create", 
            "project:task:edit", "project:task:delete", "project:task:assign",
            "project:data:export", "project:data:import", 
            "project:report:view", "project:report:create"
        ));
        
        ROLE_PERMISSIONS.put("PROJECT_ADMIN", Arrays.asList(
            "project:view", "project:edit", "project:member:view", 
            "project:member:add", "project:member:remove", "project:task:view", 
            "project:task:create", "project:task:edit", "project:task:assign",
            "project:data:export", "project:report:view", "project:report:create"
        ));
        
        ROLE_PERMISSIONS.put("PROJECT_MEMBER", Arrays.asList(
            "project:view", "project:member:view", "project:task:view", 
            "project:task:create", "project:task:edit", "project:report:view"
        ));
        
        ROLE_PERMISSIONS.put("PROJECT_VIEWER", Arrays.asList(
            "project:view", "project:member:view", "project:task:view", "project:report:view"
        ));
    }
    
    private static void initRoleMenus() {
        ROLE_MENUS.put("PROJECT_OWNER", Arrays.asList(
            "project-workspace", "project-list", "project-dashboard", 
            "project-management", "project-member", "project-task", 
            "project-report", "project-setting"
        ));
        
        ROLE_MENUS.put("PROJECT_ADMIN", Arrays.asList(
            "project-workspace", "project-list", "project-dashboard", 
            "project-management", "project-member", "project-task", "project-report"
        ));
        
        ROLE_MENUS.put("PROJECT_MEMBER", Arrays.asList(
            "project-workspace", "project-list", "project-dashboard", 
            "project-task", "project-report"
        ));
        
        ROLE_MENUS.put("PROJECT_VIEWER", Arrays.asList(
            "project-workspace", "project-list", "project-dashboard", "project-report"
        ));
    }
    
    public List<String> getRolePermissions(String roleCode) {
        return ROLE_PERMISSIONS.getOrDefault(roleCode, Collections.emptyList());
    }
    
    public List<String> getRoleMenus(String roleCode) {
        return ROLE_MENUS.getOrDefault(roleCode, Collections.emptyList());
    }
}
```

### 3.4 权限注解和AOP

#### 3.4.1 双维度权限注解
```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DualDimensionPermission {
    String value(); // 权限点
    String projectIdParam() default "projectId"; // 项目ID参数名
    boolean requireProjectContext() default false; // 是否需要项目上下文
}
```

#### 3.4.2 权限切面实现
```java
@Aspect
@Component
@RequiredArgsConstructor
public class DualDimensionPermissionAspect {
    
    private final DualDimensionPermissionService permissionService;
    
    @Around("@annotation(permission)")
    public Object checkPermission(ProceedingJoinPoint joinPoint, 
                                 DualDimensionPermission permission) throws Throwable {
        Long currentUserId = getCurrentUserId();
        Map<String, Object> context = buildPermissionContext(joinPoint, permission);
        
        if (!permissionService.hasPermission(currentUserId, permission.value(), context)) {
            throw new CoolException("权限不足，无法执行此操作");
        }
        
        return joinPoint.proceed();
    }
    
    private Map<String, Object> buildPermissionContext(ProceedingJoinPoint joinPoint, 
                                                      DualDimensionPermission permission) {
        Map<String, Object> context = new HashMap<>();
        
        if (permission.requireProjectContext()) {
            Long projectId = getProjectIdFromArgs(joinPoint.getArgs(), permission.projectIdParam());
            if (projectId != null) {
                context.put("projectId", projectId);
            }
        }
        
        return context;
    }
}
```

## 4. 前端技术实现

### 4.1 组织状态管理 (Pinia Store)

#### 4.1.1 组织Store定义
```typescript
interface OrganizationState {
  currentMode: string;
  availableModes: string[];
  userOrganizations: {
    departments: Array<{id: number, name: string, role: string}>;
    projects: Array<{id: number, name: string, role: string}>;
  };
  permissions: Record<string, boolean>;
}

export const useOrganizationStore = defineStore('organization', {
  state: (): OrganizationState => ({
    currentMode: 'DEPARTMENT',
    availableModes: [],
    userOrganizations: { departments: [], projects: [] },
    permissions: {}
  }),
  
  getters: {
    isDepartmentMode: (state) => state.currentMode === 'DEPARTMENT',
    isProjectMode: (state) => state.currentMode === 'PROJECT',
    canSwitchMode: (state) => state.availableModes.length > 1
  },
  
  actions: {
    async switchMode(targetMode: string) {
      await service.request({
        url: '/admin/organization/mode/switch',
        method: 'POST',
        data: { targetMode }
      });
      
      this.currentMode = targetMode;
      await this.loadUserPermissions();
      
      // 刷新页面以加载新的菜单
      window.location.reload();
    },
    
    async loadOrganizationInfo() {
      const response = await service.request({
        url: '/admin/organization/user/info',
        method: 'GET'
      });
      
      this.currentMode = response.currentMode;
      this.availableModes = response.availableModes;
      this.userOrganizations = response.organizations;
    }
  }
});
```

### 4.2 动态菜单系统

#### 4.2.1 菜单配置
```typescript
// 部门形态菜单
const departmentModeMenus = [
  {
    path: '/dept',
    name: '部门管理',
    icon: 'el-icon-office-building',
    children: [
      { path: '/dept/list', name: '部门列表' },
      { path: '/dept/user', name: '部门人员' }
    ]
  }
];

// 项目形态菜单
const projectModeMenus = [
  {
    path: '/project',
    name: '项目工作台',
    icon: 'el-icon-folder-opened',
    children: [
      { path: '/project/workspace', name: '工作台概览' },
      { path: '/project/list', name: '我的项目' },
      { path: '/project/task', name: '任务管理' },
      { path: '/project/report', name: '项目报表' }
    ]
  }
];

export const getDynamicMenus = () => {
  const organizationStore = useOrganizationStore();
  
  if (organizationStore.isDepartmentMode) {
    return departmentModeMenus;
  } else if (organizationStore.isProjectMode) {
    return projectModeMenus;
  }
  
  return [];
};
```

## 5. 缓存策略

### 5.1 Redis缓存设计
```
用户当前组织形态: user:current:mode:{userId} (30分钟)
用户可访问项目: user:accessible:projects:{userId} (5分钟)
用户权限信息: user:permissions:{userId} (10分钟)
用户菜单信息: user:menus:{userId} (30分钟)
项目成员关系: project:members:{projectId} (10分钟)
```

### 5.2 缓存更新策略
- 组织形态切换时清除相关缓存
- 权限变更时主动清理缓存
- 项目成员变更时更新项目缓存
- 定期刷新缓存防止数据不一致

## 6. 性能优化

### 6.1 数据库优化
- 合理设计索引，优化查询性能
- 使用批量查询减少数据库访问
- 权限查询使用JOIN优化
- 分页查询避免大数据量加载

### 6.2 应用层优化
- 权限计算结果缓存
- 菜单数据预加载
- 异步加载非关键数据
- 前端组件懒加载

## 7. 安全设计

### 7.1 权限验证
- 所有API接口进行权限验证
- 前端权限控制 + 后端权限验证
- 防止权限绕过和越权访问
- 敏感操作二次确认

### 7.2 审计日志
- 记录组织形态切换操作
- 记录权限变更操作
- 记录项目成员管理操作
- 提供完整的操作审计链

## 8. 监控和运维

### 8.1 系统监控
- 权限查询性能监控
- 缓存命中率监控
- 组织形态切换频率监控
- 用户行为分析

### 8.2 运维支持
- 提供权限诊断工具
- 支持权限数据导出
- 提供系统健康检查
- 支持数据备份和恢复

## 9. API接口设计

### 9.1 组织形态管理接口

#### 9.1.1 获取用户可用组织形态
```http
GET /admin/organization/mode/available
Response: {
  "code": 200,
  "data": {
    "currentMode": "DEPARTMENT",
    "availableModes": ["DEPARTMENT", "PROJECT"]
  }
}
```

#### 9.1.2 切换组织形态
```http
POST /admin/organization/mode/switch
Request: {
  "targetMode": "PROJECT"
}
Response: {
  "code": 200,
  "message": "切换成功"
}
```

### 9.2 项目管理接口

#### 9.2.1 获取用户项目列表
```http
GET /admin/project/user/list
Response: {
  "code": 200,
  "data": {
    "list": [
      {
        "id": 1,
        "projectName": "项目A",
        "userRole": "PROJECT_OWNER",
        "status": 1
      }
    ]
  }
}
```

#### 9.2.2 项目成员管理
```http
POST /admin/project/member/add
Request: {
  "projectId": 1,
  "userId": 2,
  "roleCode": "PROJECT_MEMBER"
}
```

### 9.3 权限验证接口

#### 9.3.1 检查用户权限
```http
GET /admin/permission/check
Params: {
  "permission": "project:edit",
  "projectId": 1
}
Response: {
  "code": 200,
  "data": {
    "hasPermission": true
  }
}
```

## 10. 数据迁移方案

### 10.1 现有数据兼容
- 现有部门用户数据自动迁移到用户组织关系表
- 保持原有部门权限体系不变
- 新增用户默认使用部门维度

### 10.2 迁移脚本
```sql
-- 迁移现有部门用户关系
INSERT INTO user_organization (user_id, organization_type, organization_id, role_code, join_time, status)
SELECT
    u.id as user_id,
    'DEPARTMENT' as organization_type,
    u.dept_id as organization_id,
    CASE
        WHEN u.role_id = 1 THEN 'DEPT_ADMIN'
        ELSE 'DEPT_MEMBER'
    END as role_code,
    u.create_time as join_time,
    1 as status
FROM base_sys_user u
WHERE u.dept_id IS NOT NULL;

-- 初始化用户当前组织形态
INSERT INTO user_current_mode (user_id, current_mode, last_switch_time)
SELECT id, 'DEPARTMENT', NOW() FROM base_sys_user;
```

## 11. 测试策略

### 11.1 单元测试
- 权限计算逻辑测试
- 组织形态切换测试
- 缓存机制测试
- 数据访问层测试

### 11.2 集成测试
- API接口测试
- 权限验证流程测试
- 前后端集成测试
- 数据库事务测试

### 11.3 性能测试
- 权限查询性能测试
- 并发用户访问测试
- 缓存性能测试
- 数据库压力测试

## 12. 部署方案

### 12.1 分阶段部署
1. **数据库升级**：执行数据库迁移脚本
2. **后端部署**：部署新版本后端服务
3. **前端部署**：部署支持双维度的前端
4. **功能验证**：验证核心功能正常
5. **全量开放**：向所有用户开放新功能

### 12.2 回滚方案
- 保留原有数据结构，支持快速回滚
- 提供数据回滚脚本
- 监控系统异常，及时响应
- 建立应急响应机制

## 13. 风险控制

### 13.1 技术风险
- **数据一致性风险**：通过事务控制和数据校验
- **性能风险**：通过缓存和查询优化
- **兼容性风险**：充分的兼容性测试

### 13.2 业务风险
- **用户适应风险**：提供详细的使用指南
- **权限混乱风险**：严格的权限验证机制
- **数据安全风险**：完善的审计和监控

## 14. 后续扩展

### 14.1 功能扩展
- 支持更多组织维度（如地域维度）
- 项目模板和权限模板
- 高级权限配置功能
- 组织架构可视化

### 14.2 技术扩展
- 微服务架构改造
- 分布式权限管理
- 实时权限同步
- AI辅助权限推荐

## 15. 总结

双维度组织架构系统通过以下技术手段实现了设计目标：

1. **数据模型设计**：通过用户组织关系表实现双维度关联
2. **权限体系**：基于当前组织形态的动态权限计算
3. **缓存策略**：多层缓存提升系统性能
4. **前端架构**：组织状态管理和动态菜单系统
5. **安全保障**：完善的权限验证和审计机制

该系统既保持了现有部门权限体系的稳定性，又提供了灵活的项目维度管理能力，为企业的矩阵式管理提供了强有力的技术支撑。
