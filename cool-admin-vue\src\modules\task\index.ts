import { type ModuleConfig } from '/@/cool';

export default (): ModuleConfig => {
  return {
    services: [
      {
        path: '/admin/task/package',
        name: 'taskPackage'
      },
      {
        path: '/admin/task/info',
        name: 'info',
        options: {
          // 自定义API方法
          api: {
            executionDetails: 'GET /execution-details/:taskId',
            batchExecutionDetails: 'POST /batch-execution-details'
          }
        }
      },
      {
        path: '/admin/sop/step',
        name: 'step'
      },
      {
        path: '/admin/task/assignment',
        name: 'assignment',
        options: {
          // 自定义API方法
          api: {
            execute: 'POST /execute',
            single: 'POST /single/:taskId',
            package: 'POST /package/:packageId',
            candidates: {
              task: 'GET /candidates/:taskId',
              all: 'GET /candidates',
              byRoles: 'POST /candidates/by-roles'
            },
            validate: 'POST /validate/:taskId',
            manual: 'POST /manual'
          }
        }
      },
      {
        path: '/admin/task/execution',
        name: 'execution'
      },
      {
        path: '/admin/task/status',
        name: 'status',
        options: {
          // 自定义API方法
          api: {
            completeTaskExecution: 'POST /task/execution/complete',
            forceCompleteTask: 'POST /task/force-complete',
            closeTask: 'POST /task/close',
            reopenTask: 'POST /task/reopen',
            canCompleteTask: 'GET /task/canComplete',
            canCloseTask: 'GET /task/canClose',
            batchForceCompleteTask: 'POST /task/batch/force-complete',
            batchCloseTask: 'POST /task/batch/close',
            batchReopenTask: 'POST /task/batch/reopen'
          }
        }
      }
    ]
  };
};
