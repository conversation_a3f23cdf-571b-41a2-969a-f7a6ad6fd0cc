package com.cool.modules.sop.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.cool.core.base.BaseServiceImpl;
import com.cool.modules.sop.dto.ai.QualityCheckRequest;
import com.cool.modules.sop.entity.TaskExecuteRecordEntity;
import com.cool.modules.sop.mapper.TaskExecuteRecordMapper;
import com.cool.modules.sop.service.AILLMService;
import com.cool.modules.sop.service.TaskExecuteRecordService;
import com.cool.modules.task.enums.TaskBusinessStatusEnum;
// import com.cool.modules.sop.dto.ai.QualityCheckResult;
import com.mybatisflex.core.query.QueryWrapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 任务执行记录服务实现类
 */
@Slf4j
@Service
public class TaskExecuteRecordServiceImpl extends BaseServiceImpl<TaskExecuteRecordMapper, TaskExecuteRecordEntity> implements TaskExecuteRecordService {

    @Autowired
    @Lazy
    private AILLMService aiLLMService;

    @Override
    @Transactional
    public void startTask(Long recordId) {
        TaskExecuteRecordEntity record = getById(recordId);
        if (record == null) {
            throw new RuntimeException("任务记录不存在");
        }
        
        if (!TaskBusinessStatusEnum.PENDING_EXECUTE.getCode().equals(record.getStatus())) {
            throw new RuntimeException("只能启动待执行状态的任务");
        }

        record.setStatus(TaskBusinessStatusEnum.EXECUTING.getCode());
        record.setActualStartTime(new Date());
        updateById(record);
        
        log.info("任务已启动，ID: {}", recordId);
    }

    @Override
    @Transactional
    public void pauseTask(Long recordId) {
        TaskExecuteRecordEntity record = getById(recordId);
        if (record == null) {
            throw new RuntimeException("任务记录不存在");
        }
        
        if (!TaskBusinessStatusEnum.EXECUTING.getCode().equals(record.getStatus())) {
            throw new RuntimeException("只能暂停执行中的任务");
        }

        // 注意：TaskBusinessStatusEnum没有PAUSED状态，暂停功能需要重新设计
        // 暂时设置为待执行状态
        record.setStatus(TaskBusinessStatusEnum.PENDING_EXECUTE.getCode());
        updateById(record);
        
        log.info("任务已暂停，ID: {}", recordId);
    }

    @Override
    @Transactional
    public void resumeTask(Long recordId) {
        TaskExecuteRecordEntity record = getById(recordId);
        if (record == null) {
            throw new RuntimeException("任务记录不存在");
        }
        
        if (!TaskBusinessStatusEnum.PENDING_EXECUTE.getCode().equals(record.getStatus())) {
            throw new RuntimeException("只能恢复待执行状态的任务");
        }

        record.setStatus(TaskBusinessStatusEnum.EXECUTING.getCode());
        updateById(record);
        
        log.info("任务已恢复，ID: {}", recordId);
    }

    @Override
    @Transactional
    public void completeTask(Long recordId) {
        completeTask(recordId, null);
    }
    
    @Transactional
    public void completeTask(Long recordId, String result) {
        TaskExecuteRecordEntity record = getById(recordId);
        if (record == null) {
            throw new RuntimeException("任务记录不存在");
        }
        
        if (!TaskBusinessStatusEnum.EXECUTING.getCode().equals(record.getStatus())) {
            throw new RuntimeException("只能完成执行中的任务");
        }

        record.setStatus(TaskBusinessStatusEnum.COMPLETED.getCode());
        record.setActualEndTime(new Date());
        record.setExecutionResult(result);
        record.setProgress(100);
        
        // 计算实际工时
        // TODO: Add actualDuration field to entity
        // if (record.getActualStartTime() != null) {
        //     long duration = (record.getActualEndTime().getTime() - record.getActualStartTime().getTime()) / (1000 * 60);
        //     record.setActualDuration((int) duration);
        // }
        
        updateById(record);
        
        log.info("任务已完成，ID: {}", recordId);
    }

    @Transactional
    public void failTask(Long recordId, String reason) {
        TaskExecuteRecordEntity record = getById(recordId);
        if (record == null) {
            throw new RuntimeException("任务记录不存在");
        }
        
        // 注意：TaskBusinessStatusEnum没有FAILED状态，失败任务设置为已关闭
        record.setStatus(TaskBusinessStatusEnum.CLOSED.getCode());
        record.setActualEndTime(new Date());
        record.setExecutionResult("执行失败: " + reason);
        
        updateById(record);
        
        log.info("任务已标记为失败，ID: {}, 原因: {}", recordId, reason);
    }

    public void updateProgress(Long recordId, Integer progress) {
        TaskExecuteRecordEntity record = getById(recordId);
        if (record == null) {
            throw new RuntimeException("任务记录不存在");
        }
        
        record.setProgress(progress);
        updateById(record);
    }

    public com.cool.modules.sop.dto.ai.CommonAITypes.ExecutionGuidance getAIGuidance(Long recordId) {
        TaskExecuteRecordEntity record = getById(recordId);
        if (record == null) {
            throw new RuntimeException("任务记录不存在");
        }
        
        try {
            return aiLLMService.getExecutionGuidance(recordId, "Current situation");
            
        } catch (Exception e) {
            log.error("获取AI执行指导失败", e);
            throw new RuntimeException("获取AI执行指导失败: " + e.getMessage());
        }
    }

    public Map<String, Object> performQualityCheck(Long recordId) {
        TaskExecuteRecordEntity record = getById(recordId);
        if (record == null) {
            throw new RuntimeException("任务记录不存在");
        }
        
        if (!TaskBusinessStatusEnum.COMPLETED.getCode().equals(record.getStatus())) {
            throw new RuntimeException("只能对已完成的任务进行质量检查");
        }
        
        try {
            QualityCheckRequest request = QualityCheckRequest.builder()
                .stepId(record.getSopStepId())
                .executionResult(record.getExecutionResult())
                .executionTimeMinutes(record.getActualTime())
                .build();
                
            Map<String, Object> result = aiLLMService.qualityCheck(recordId, new HashMap<>());
            
            // 保存质量检查结果
            record.setQualityScore((Integer) result.get("qualityScore"));
            record.setQualityCheckResult((String) result.get("feedback"));
            updateById(record);
            
            return result;
            
        } catch (Exception e) {
            log.error("AI质量检查失败", e);
            throw new RuntimeException("AI质量检查失败: " + e.getMessage());
        }
    }

    public List<TaskExecuteRecordEntity> getByWorkOrderId(Long workOrderId) {
        QueryWrapper wrapper = QueryWrapper.create()
            .eq("work_order_id", workOrderId)
            .orderBy("step_order", true);
            
        return list(wrapper);
    }

    public List<TaskExecuteRecordEntity> getByExecutorId(Long executorId) {
        QueryWrapper wrapper = QueryWrapper.create()
            .eq("executor_id", executorId)
            .orderBy("create_time", false);
            
        return list(wrapper);
    }

    public Map<String, Object> getTaskStats(Long executorId) {
        Map<String, Object> stats = new HashMap<>();
        
        QueryWrapper wrapper = QueryWrapper.create();
        if (executorId != null) {
            wrapper.eq("executor_id", executorId);
        }
        
        // 统计各状态任务数量
        for (TaskBusinessStatusEnum status : TaskBusinessStatusEnum.values()) {
            QueryWrapper statusWrapper = wrapper.clone().eq("status", status.getCode());
            long count = count(statusWrapper);
            stats.put(status.getCode().toString(), count);
        }
        
        // 计算完成率
        long totalCount = count(wrapper);
        long completedCount = (Long) stats.get(TaskBusinessStatusEnum.COMPLETED.getCode().toString());
        double completionRate = totalCount > 0 ? (double) completedCount / totalCount * 100 : 0;
        stats.put("completionRate", Math.round(completionRate * 100.0) / 100.0);
        
        // 计算平均质量评分
        QueryWrapper qualityWrapper = wrapper.clone()
            .eq("status", TaskBusinessStatusEnum.COMPLETED.getCode())
            .isNotNull("quality_score");
        List<TaskExecuteRecordEntity> completedRecords = list(qualityWrapper);
        if (!completedRecords.isEmpty()) {
            double avgQuality = completedRecords.stream()
                .mapToInt(TaskExecuteRecordEntity::getQualityScore)
                .average()
                .orElse(0.0);
            stats.put("averageQuality", Math.round(avgQuality * 100.0) / 100.0);
        } else {
            stats.put("averageQuality", 0.0);
        }
        
        stats.put("totalCount", totalCount);
        
        return stats;
    }

    public List<TaskExecuteRecordEntity> getOverdueTasks() {
        QueryWrapper wrapper = QueryWrapper.create()
            .in("status", TaskBusinessStatusEnum.PENDING_EXECUTE.getCode(), TaskBusinessStatusEnum.EXECUTING.getCode())
            .lt("planned_end_time", new Date());
            
        return list(wrapper);
    }

    public void batchAssign(List<Long> recordIds, Long executorId, String executorName) {
        for (Long recordId : recordIds) {
            TaskExecuteRecordEntity record = getById(recordId);
            if (record != null && TaskBusinessStatusEnum.PENDING_ASSIGN.getCode().equals(record.getStatus())) {
                record.setExecutorId(executorId);
                record.setExecutorName(executorName);
                record.setStatus(TaskBusinessStatusEnum.PENDING_EXECUTE.getCode());
                updateById(record);
            }
        }
        log.info("批量分配任务完成，任务数量: {}, 分配给: {}", recordIds.size(), executorName);
    }

    public Map<String, Object> getExecutionAnalytics(Long workOrderId) {
        Map<String, Object> analytics = new HashMap<>();
        
        QueryWrapper wrapper = QueryWrapper.create();
        if (workOrderId != null) {
            wrapper.eq("work_order_id", workOrderId);
        }
        
        List<TaskExecuteRecordEntity> records = list(wrapper);
        
        if (records.isEmpty()) {
            return analytics;
        }
        
        // 计算总体进度
        int totalProgress = records.stream()
            .mapToInt(TaskExecuteRecordEntity::getProgress)
            .sum();
        analytics.put("overallProgress", totalProgress / records.size());
        
        // 计算延期任务数量
        long overdueCount = records.stream()
            .filter(r -> r.getPlannedEndTime() != null && 
                        r.getPlannedEndTime().before(new Date()) &&
                        !TaskBusinessStatusEnum.COMPLETED.getCode().equals(r.getStatus()))
            .count();
        analytics.put("overdueCount", overdueCount);
        
        // 计算风险评估
        String riskLevel = "LOW";
        if (overdueCount > records.size() * 0.3) {
            riskLevel = "HIGH";
        } else if (overdueCount > records.size() * 0.1) {
            riskLevel = "MEDIUM";
        }
        analytics.put("riskLevel", riskLevel);
        
        // 计算预计完成时间
        Date maxPlannedEndTime = records.stream()
            .filter(r -> r.getPlannedEndTime() != null)
            .map(TaskExecuteRecordEntity::getPlannedEndTime)
            .max(Date::compareTo)
            .orElse(null);
        analytics.put("estimatedCompletionTime", maxPlannedEndTime);
        
        return analytics;
    }

    @Override
    public void addExecutionLog(Long taskId, String logContent, String logType) {
        // Implementation for adding execution log
        log.info("添加执行日志 - 任务ID: {}, 类型: {}, 内容: {}", taskId, logType, logContent);
    }

    @Override
    public List<Map<String, Object>> getExecutionLogs(Long taskId) {
        // Implementation for getting execution logs
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> qualityCheck(Long taskId, Map<String, Object> checkResult) {
        // Implementation for quality check
        return checkResult;
    }

    @Override
    public List<TaskExecuteRecordEntity> getTasksByFilter(String filter, Long assigneeId) {
        QueryWrapper wrapper = QueryWrapper.create();
        if (assigneeId != null) {
            wrapper.eq("executor_id", assigneeId);
        }
        if (filter != null) {
            wrapper.eq("status", filter);
        }
        return list(wrapper);
    }

    @Override
    public Map<String, Object> getTaskDetail(Long taskId) {
        TaskExecuteRecordEntity task = getById(taskId);
        Map<String, Object> detail = new HashMap<>();
        detail.put("task", task);
        return detail;
    }

    // 辅助方法
    private Map<String, Object> parseExecutionContext(TaskExecuteRecordEntity record) {
        Map<String, Object> context = new HashMap<>();
        context.put("taskName", record.getTaskName());
        context.put("taskDescription", record.getTaskDescription());
        context.put("estimatedTime", record.getEstimatedTime());
        context.put("currentProgress", record.getProgress());
        context.put("executorId", record.getExecutorId());
        context.put("executorName", record.getExecutorName());
        
        if (record.getActualStartTime() != null) {
            long elapsedTime = (new Date().getTime() - record.getActualStartTime().getTime()) / (1000 * 60);
            context.put("elapsedTime", elapsedTime);
        }
        
        return context;
    }
} 