package com.cool.modules.sop.controller.admin;


import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.core.request.R;
import com.cool.modules.sop.entity.SOPScenarioEntity;
import com.cool.modules.sop.service.SOPScenarioService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import cn.hutool.json.JSONObject;
import jakarta.servlet.http.HttpServletRequest;

import java.util.List;
import java.util.Map;

/**
 * SOP场景管理控制器
 */
@Tag(name = "SOP场景管理", description = "SOP场景管理相关接口")
@CoolRestController(api = {"add", "delete", "update", "page", "list", "info"})
public class AdminSOPScenarioController extends BaseController<SOPScenarioService, SOPScenarioEntity> {

    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        // 支持按行业、阶段、状态筛选
        if (requestParams.containsKey("industryCode")) {
            requestParams.set("industryCode", requestParams.getStr("industryCode"));
        }
        if (requestParams.containsKey("phaseCode")) {
            requestParams.set("phaseCode", requestParams.getStr("phaseCode"));
        }
        if (requestParams.containsKey("status")) {
            requestParams.set("status", requestParams.getInt("status"));
        }
        // setPageOption(createOp().fieldEq("moduleId", "industryId")
        //         .fieldLike("scenarioName", "scenarioCode")
        //         .perm("asc", "id"));
    }

    @Operation(summary = "根据行业获取SOP场景")
    @GetMapping("/industry/{industryCode}")
    public R getByIndustry(@Parameter(description = "行业编码") @PathVariable String industryCode) {
        try {
            List<SOPScenarioEntity> scenarios = service.getByIndustry(industryCode);
            return R.ok(scenarios);
        } catch (Exception e) {
            return R.error("获取SOP场景失败：" + e.getMessage());
        }
    }

    @Operation(summary = "根据阶段获取SOP场景")
    @GetMapping("/phase/{phaseCode}")
    public R getByPhase(@Parameter(description = "阶段编码") @PathVariable String phaseCode) {
        try {
            List<SOPScenarioEntity> scenarios = service.getByPhase(phaseCode);
            return R.ok(scenarios);
        } catch (Exception e) {
            return R.error("获取SOP场景失败：" + e.getMessage());
        }
    }

    @Operation(summary = "复制SOP场景")
    @PostMapping("/copy/{id}")
    public R copyScenario(@Parameter(description = "场景ID") @PathVariable Long id,
                         @RequestBody Map<String, String> params) {
        try {
            String newScenarioName = params.get("scenarioName");
            String newScenarioCode = params.get("scenarioCode");
            SOPScenarioEntity newScenario = service.copyScenario(id, newScenarioName, newScenarioCode);
            return R.ok(newScenario);
        } catch (Exception e) {
            return R.error("复制SOP场景失败：" + e.getMessage());
        }
    }

    @Operation(summary = "发布SOP场景")
    @PostMapping("/publish/{id}")
    public R publishScenario(@Parameter(description = "场景ID") @PathVariable Long id) {
        try {
            service.publishScenario(id);
            return R.ok("SOP场景发布成功");
        } catch (Exception e) {
            return R.error("发布SOP场景失败：" + e.getMessage());
        }
    }

    @Operation(summary = "归档SOP场景")
    @PostMapping("/archive/{id}")
    public R archiveScenario(@Parameter(description = "场景ID") @PathVariable Long id) {
        try {
            service.archiveScenario(id);
            return R.ok("SOP场景归档成功");
        } catch (Exception e) {
            return R.error("归档SOP场景失败：" + e.getMessage());
        }
    }

    @Operation(summary = "更新版本")
    @PostMapping("/version/{id}")
    public R updateVersion(@Parameter(description = "场景ID") @PathVariable Long id,
                          @RequestBody Map<String, String> params) {
        try {
            String newVersion = params.get("version");
            service.updateVersion(id, newVersion);
            return R.ok("版本更新成功");
        } catch (Exception e) {
            return R.error("版本更新失败：" + e.getMessage());
        }
    }

    @Operation(summary = "AI转换执行周期")
    @PostMapping("/ai/convert-cycle/{id}")
    public R convertExecutionCycle(@Parameter(description = "场景ID") @PathVariable Long id) {
        try {
            service.convertExecutionCycleByAI(id);
            return R.ok("执行周期转换成功");
        } catch (Exception e) {
            return R.error("执行周期转换失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取SOP场景统计")
    @GetMapping("/stats")
    public R getScenarioStats(@Parameter(description = "行业编码") @RequestParam(required = false) String industryCode) {
        try {
            Map<String, Object> stats = service.getScenarioStats(industryCode);
            return R.ok(stats);
        } catch (Exception e) {
            return R.error("获取统计信息失败：" + e.getMessage());
        }
    }

    @Operation(summary = "导出SOP场景")
    @GetMapping("/export")
    public R exportScenarios(@Parameter(description = "导出条件") @RequestParam Map<String, Object> params) {
        try {
            // 这里可以实现导出功能
            return R.ok("导出成功");
        } catch (Exception e) {
            return R.error("导出失败：" + e.getMessage());
        }
    }

    @Operation(summary = "根据场景生成任务")
    @PostMapping("/generateTasks")
    public R generateTasks(@RequestBody JSONObject requestParams) {
        Long scenarioId = requestParams.getLong("scenarioId");
        service.generateTasksFromScenario(scenarioId);
        return R.ok();
    }

    @Operation(summary = "修复所有场景的总步骤数")
    @PostMapping("/fixTotalSteps")
    public R fixAllScenarioTotalSteps() {
        try {
            service.fixAllScenarioTotalSteps();
            return R.ok("场景总步骤数修复完成");
        } catch (Exception e) {
            return R.error("修复失败：" + e.getMessage());
        }
    }
} 