package com.cool.modules.sop.service.impl;

import com.cool.core.base.BaseServiceImpl;
import com.cool.modules.sop.entity.WorkOrderEntity;
import com.cool.modules.sop.mapper.WorkOrderMapper;
import com.cool.modules.sop.service.WorkOrderService;
import com.cool.modules.sop.service.AILLMService;
import com.cool.modules.sop.dto.ai.TaskScheduleResult;
import com.cool.modules.sop.enums.WorkOrderStatusEnum;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.StrUtil;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Date;
import java.util.ArrayList;
import com.cool.modules.task.entity.TaskPackageEntity;
import com.cool.modules.task.service.TaskInfoService;
import com.cool.modules.task.service.TaskPackageService;
import com.cool.modules.sop.service.impl.AILLMServiceImpl;
import com.cool.modules.task.entity.TaskInfoEntity;

/**
 * 工作工单服务实现类
 */
@Slf4j
@Service
public class WorkOrderServiceImpl extends BaseServiceImpl<WorkOrderMapper, WorkOrderEntity> implements WorkOrderService {

    @Autowired
    private AILLMService aiLLMService;
    
    @Autowired
    private TaskPackageService taskPackageService;

    @Autowired
    private TaskInfoService taskInfoService;
    
    @Override
    public Long add(WorkOrderEntity workOrder) {
        if (StrUtil.isBlank(workOrder.getOrderNo())) {
            workOrder.setOrderNo(generateOrderNo());
        }
        return super.add(workOrder);
    }

    @Override
    public void batchAssign(List<Long> workOrderIds, Long assigneeId) {
        for (Long workOrderId : workOrderIds) {
            WorkOrderEntity workOrder = getById(workOrderId);
            if (workOrder != null) {
                workOrder.setAssigneeId(assigneeId);
                updateById(workOrder);
            }
        }
        log.info("批量分配工单完成，工单数量: {}, 分配给: {}", workOrderIds.size(), assigneeId);
    }

    @Override
    @Transactional
    public void startWorkOrder(Long workOrderId) {
        WorkOrderEntity workOrder = getById(workOrderId);
        if (workOrder == null) {
            throw new RuntimeException("工单不存在");
        }
        
        if (!WorkOrderStatusEnum.PENDING.getCode().equals(workOrder.getStatus())) {
            throw new RuntimeException("只能启动待处理状态的工单");
        }
        
        workOrder.setStatus(WorkOrderStatusEnum.IN_PROGRESS.getCode());
        workOrder.setActualStartTime(new Date());
        updateById(workOrder);
        
        log.info("工单已启动，ID: {}", workOrderId);
    }

    @Override
    @Transactional
    public void pauseWorkOrder(Long workOrderId) {
        WorkOrderEntity workOrder = getById(workOrderId);
        if (workOrder == null) {
            throw new RuntimeException("工单不存在");
        }
        
        if (!WorkOrderStatusEnum.IN_PROGRESS.getCode().equals(workOrder.getStatus())) {
            throw new RuntimeException("只能暂停进行中的工单");
        }
        
        workOrder.setStatus(WorkOrderStatusEnum.PAUSED.getCode());
        updateById(workOrder);
        
        log.info("工单已暂停，ID: {}", workOrderId);
    }

    @Override
    @Transactional
    public void resumeWorkOrder(Long workOrderId) {
        WorkOrderEntity workOrder = getById(workOrderId);
        if (workOrder == null) {
            throw new RuntimeException("工单不存在");
        }
        
        if (!WorkOrderStatusEnum.PAUSED.getCode().equals(workOrder.getStatus())) {
            throw new RuntimeException("只能恢复暂停状态的工单");
        }
        
        workOrder.setStatus(WorkOrderStatusEnum.IN_PROGRESS.getCode());
        updateById(workOrder);
        
        log.info("工单已恢复，ID: {}", workOrderId);
    }

    @Override
    @Transactional
    public void completeWorkOrder(Long workOrderId) {
        WorkOrderEntity workOrder = getById(workOrderId);
        if (workOrder == null) {
            throw new RuntimeException("工单不存在");
        }
        
        if (!WorkOrderStatusEnum.IN_PROGRESS.getCode().equals(workOrder.getStatus())) {
            throw new RuntimeException("只能完成进行中的工单");
        }
        
        workOrder.setStatus(WorkOrderStatusEnum.COMPLETED.getCode());
        workOrder.setActualEndTime(new Date());
        updateById(workOrder);
        
        log.info("工单已完成，ID: {}", workOrderId);
    }

    @Override
    @Transactional
    public void cancelWorkOrder(Long workOrderId, String reason) {
        WorkOrderEntity workOrder = getById(workOrderId);
        if (workOrder == null) {
            throw new RuntimeException("工单不存在");
        }
        
        if (WorkOrderStatusEnum.COMPLETED.getCode().equals(workOrder.getStatus()) ||
            WorkOrderStatusEnum.CANCELLED.getCode().equals(workOrder.getStatus())) {
            throw new RuntimeException("已完成或已取消的工单无法取消");
        }
        
        workOrder.setStatus(WorkOrderStatusEnum.CANCELLED.getCode());
        workOrder.setRemark(workOrder.getRemark() + "\n取消原因: " + reason);
        updateById(workOrder);
        
        log.info("工单已取消，ID: {}, 原因: {}", workOrderId, reason);
    }

    @Override
    public TaskScheduleResult intelligentSchedule(List<Long> workOrderIds) {
        log.info("开始智能调度，工单数量: {}", workOrderIds.size());
        
        try {
            // 调用AI服务进行智能调度
            TaskScheduleResult result = aiLLMService.scheduleTask(workOrderIds);
            
            if (result.getSuccess()) {
                // 如果调度成功，可以在这里执行调度结果的应用逻辑
                log.info("智能调度完成，预期完成时间: {}", result.getExpectedCompletionTime());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("智能调度失败", e);
            throw new RuntimeException("智能调度失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getWorkOrderStats(Long assigneeId) {
        Map<String, Object> stats = new HashMap<>();
        
        QueryWrapper wrapper = QueryWrapper.create();
        if (assigneeId != null) {
            wrapper.eq("assignee_id", assigneeId);
        }
        
        // 统计各状态工单数量
        for (WorkOrderStatusEnum status : WorkOrderStatusEnum.values()) {
            QueryWrapper statusWrapper = wrapper.clone().eq("status", status.getCode());
            long count = count(statusWrapper);
            stats.put(status.getCode().toString(), count);
        }
        
        // 计算完成率
        long totalCount = count(wrapper);
        long completedCount = (Long) stats.get(WorkOrderStatusEnum.COMPLETED.getCode());
        double completionRate = totalCount > 0 ? (double) completedCount / totalCount * 100 : 0;
        stats.put("completionRate", Math.round(completionRate * 100.0) / 100.0);
        
        // 总数
        stats.put("totalCount", totalCount);
        
        return stats;
    }

    @Override
    public List<WorkOrderEntity> getHighPriorityWorkOrders(Integer limit) {
        QueryWrapper wrapper = QueryWrapper.create()
            .eq("status", WorkOrderStatusEnum.PENDING.getCode())
            .orderBy("priority", false)
            .orderBy("planned_start_time", true);
            
        if (limit != null && limit > 0) {
            wrapper.limit(limit);
        }
        
        return list(wrapper);
    }

    @Override
    public List<WorkOrderEntity> getOverdueWorkOrders() {
        QueryWrapper wrapper = QueryWrapper.create()
            .in("status", WorkOrderStatusEnum.PENDING.getCode(), WorkOrderStatusEnum.IN_PROGRESS.getCode())
            .lt("planned_end_time", new Date());
            
        return list(wrapper);
    }

    @Override
    public void applyScheduleResult(TaskScheduleResult scheduleResult) {
        if (scheduleResult == null || !scheduleResult.getSuccess()) {
            log.warn("无效的调度结果，跳过应用");
            return;
        }

        if (scheduleResult.getAssignments() != null) {
            for (TaskScheduleResult.TaskAssignment assignment : scheduleResult.getAssignments()) {
                if (assignment.getTaskId() != null) {
                    WorkOrderEntity workOrder = getById(assignment.getTaskId());
                    if (workOrder != null) {
                        workOrder.setAssigneeId(assignment.getAssigneeId());
                        workOrder.setAssigneeName(assignment.getAssigneeName());
                        workOrder.setPlannedStartTime(assignment.getPlannedStartTime());
                        workOrder.setPlannedEndTime(assignment.getPlannedEndTime());
                        updateById(workOrder);
                    }
                }
            }
        }
        log.info("调度结果应用完成");
    }

    @Override
    public Map<String, Object> getWorkOrderStats(Long assigneeId, String timeRange) {
        Map<String, Object> stats = new HashMap<>();
        
        QueryWrapper wrapper = QueryWrapper.create();
        if (assigneeId != null) {
            wrapper.eq("assignee_id", assigneeId);
        }

        // 根据时间范围过滤
        if ("today".equals(timeRange)) {
            wrapper.ge("create_time", getTodayStart());
        } else if ("week".equals(timeRange)) {
            wrapper.ge("create_time", getWeekStart());
        } else if ("month".equals(timeRange)) {
            wrapper.ge("create_time", getMonthStart());
        }
        
        // 统计各状态工单数量
        for (WorkOrderStatusEnum status : WorkOrderStatusEnum.values()) {
            QueryWrapper statusWrapper = wrapper.clone().eq("status", status.getCode());
            long count = count(statusWrapper);
            stats.put(status.getCode().toString(), count);
        }
        
        // 计算完成率
        long totalCount = count(wrapper);
        long completedCount = (Long) stats.get(WorkOrderStatusEnum.COMPLETED.getCode().toString());
        double completionRate = totalCount > 0 ? (double) completedCount / totalCount * 100 : 0;
        stats.put("completionRate", Math.round(completionRate * 100.0) / 100.0);
        
        stats.put("totalCount", totalCount);
        
        return stats;
    }

    @Override
    public Map<String, Object> getWorkOrderDetail(Long workOrderId) {
        Map<String, Object> detail = new HashMap<>();
        WorkOrderEntity workOrder = getById(workOrderId);
        if (workOrder == null) {
            return null;
        }
        detail.put("workOrder", workOrder);

        // 查询关联的任务包
        List<TaskPackageEntity> taskPackages = taskPackageService.list(
            QueryWrapper.create().eq(TaskPackageEntity::getWorkOrderId, workOrderId)
        );

        // 为每个任务包查询其下的任务
        List<Map<String, Object>> packagesWithTasks = new ArrayList<>();
        if (taskPackages != null && !taskPackages.isEmpty()) {
            for (TaskPackageEntity pkg : taskPackages) {
                Map<String, Object> packageDetail = new HashMap<>();
                packageDetail.put("packageInfo", pkg);
                
                List<TaskInfoEntity> tasks = taskInfoService.list(
                    QueryWrapper.create().eq("task_package_id", pkg.getId())
                );
                packageDetail.put("tasks", tasks);
                packagesWithTasks.add(packageDetail);
            }
        }
        
        detail.put("taskPackages", packagesWithTasks);

        return detail;
    }

    @Override
    public WorkOrderEntity createFromTemplate(Long templateId, WorkOrderEntity workOrder) {
        // 这里需要SOP模板服务支持
        // SOPTemplateEntity template = sopTemplateService.getById(templateId);
        
        workOrder.setSopTemplateId(templateId);
        workOrder.setStatus(WorkOrderStatusEnum.PENDING.getCode());
        workOrder.setProgress(0);
        workOrder.setCreateTime(new Date());
        
        // 生成工单编号
        workOrder.setOrderNo(generateOrderNo());
        
        save(workOrder);
        return workOrder;
    }

    @Override
    public Map<String, Object> getWorkOrderProgress(Long workOrderId) {
        Map<String, Object> progress = new HashMap<>();
        
        WorkOrderEntity workOrder = getById(workOrderId);
        if (workOrder == null) {
            throw new RuntimeException("工单不存在");
        }
        
        progress.put("workOrderId", workOrderId);
        progress.put("status", workOrder.getStatus());
        progress.put("progress", workOrder.getProgress());
        progress.put("plannedStartTime", workOrder.getPlannedStartTime());
        progress.put("plannedEndTime", workOrder.getPlannedEndTime());
        progress.put("actualStartTime", workOrder.getActualStartTime());
        progress.put("actualEndTime", workOrder.getActualEndTime());
        progress.put("estimatedWorkTime", workOrder.getEstimatedWorkTime());
        progress.put("actualWorkTime", workOrder.getActualWorkTime());
        
        return progress;
    }

    @Override
    public List<Map<String, Object>> getExecutionHistory(Long workOrderId) {
        List<Map<String, Object>> history = new ArrayList<>();
        
        // 这里需要执行记录服务支持
        // List<TaskExecuteRecordEntity> records = taskExecuteRecordService.getByWorkOrderId(workOrderId);
        
        // 模拟执行历史数据
        Map<String, Object> record = new HashMap<>();
        record.put("action", "工单创建");
        record.put("operator", "系统");
        record.put("operateTime", new Date());
        record.put("description", "工单已创建");
        history.add(record);
        
        return history;
    }

    // 辅助方法
    private String generateOrderNo() {
        return "WO" + System.currentTimeMillis();
    }

    private Date getTodayStart() {
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.set(java.util.Calendar.HOUR_OF_DAY, 0);
        cal.set(java.util.Calendar.MINUTE, 0);
        cal.set(java.util.Calendar.SECOND, 0);
        cal.set(java.util.Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    private Date getWeekStart() {
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.set(java.util.Calendar.DAY_OF_WEEK, cal.getFirstDayOfWeek());
        cal.set(java.util.Calendar.HOUR_OF_DAY, 0);
        cal.set(java.util.Calendar.MINUTE, 0);
        cal.set(java.util.Calendar.SECOND, 0);
        cal.set(java.util.Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    private Date getMonthStart() {
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.set(java.util.Calendar.DAY_OF_MONTH, 1);
        cal.set(java.util.Calendar.HOUR_OF_DAY, 0);
        cal.set(java.util.Calendar.MINUTE, 0);
        cal.set(java.util.Calendar.SECOND, 0);
        cal.set(java.util.Calendar.MILLISECOND, 0);
        return cal.getTime();
    }
} 