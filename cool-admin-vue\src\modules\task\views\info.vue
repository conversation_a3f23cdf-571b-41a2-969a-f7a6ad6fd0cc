<template>
	<cl-crud ref="Crud" @load="onLoad">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<!-- 高级搜索按钮 -->
			<cl-adv-btn />
			<cl-flex1 />
			
			<!-- 部门筛选器 -->
			<department-filter 
				v-model="selectedDepartments"
				@change="onDepartmentFilterChange"
				style="margin-right: 10px; width: 240px;"
			/>
			
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<!-- 高级搜索 -->
		<cl-adv-search ref="AdvSearch" />

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table">
				<!-- 任务类型列 -->
				<template #column-taskCategory="{ scope }">
					<el-tag
						:type="getTaskCategoryColor(scope.row.taskCategory)"
						size="small"
					>
						{{ getTaskCategoryLabel(scope.row.taskCategory) }}
					</el-tag>
				</template>

				<!-- 任务状态列 -->
				<template #column-taskStatus="{ scope }">
					<el-tag
						:type="getTaskStatusColor(scope.row.taskStatus)"
						:class="{ 'custom-yellow-tag': scope.row.taskStatus === 4 }"
						size="small"
					>
						{{ getTaskStatusLabel(scope.row.taskStatus) }}
					</el-tag>
				</template>
			</cl-table>
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "task-info",
});

import { useCrud, useTable, useUpsert, useSearch, useAdvSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";
import { h, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import DepartmentFilter from "../components/DepartmentFilter.vue";
import DepartmentTag from "../components/DepartmentTag.vue";
import {
  taskCategoryOptions,
  taskBusinessStatusOptions,
  taskSourceOptions,
  taskScheduleStatusOptions,
  yesNoBoolOptions,
  getTaskCategoryLabel,
  getTaskCategoryColor,
  getTaskStatusLabel,
  getTaskStatusColor,
  getTaskSourceLabel,
  getTaskSourceColor,
  getYesNoBoolLabel,
  getYesNoBoolColor
} from "../dict";

const { service } = useCool();
const { t } = useI18n();

// 部门筛选相关
const selectedDepartments = ref([]);

// CRUD 配置
const Crud = useCrud({
  service: service.task.info
}, (app) => {
  app.refresh()
});

// 表格配置
const Table = useTable({
  onRefresh: (params, { next }) => {
    // 应用部门筛选参数
    if (selectedDepartments.value && selectedDepartments.value.length > 0) {
      params.departmentIds = selectedDepartments.value;
    }
    next(params);
  },
  columns: [
    {
      type: "selection",
      width: 60
    },
    {
      label: "场景名称",
      prop: "scenarioName",
      minWidth: 150,
      showOverflowTooltip: true
    },
    {
      label: "步骤名称",
      prop: "stepName",
      minWidth: 150,
      showOverflowTooltip: true
    },
    {
      label: "步骤编码",
      prop: "stepCode",
      width: 120,
      showOverflowTooltip: true
    },
    {
      label: "任务名称",
      prop: "name",
      minWidth: 200,
      showOverflowTooltip: true
    },
    {
      label: "所属部门",
      prop: "departmentName",
      minWidth: 120,
      render: (scope) => {
        return h(DepartmentTag, {
          departmentId: scope.departmentId,
          departmentName: scope.departmentName,
          size: "small",
          clickable: true,
          onClick: () => {
            // 点击部门标签时筛选该部门
            selectedDepartments.value = [scope.departmentId];
            onDepartmentFilterChange([scope.departmentId]);
          }
        });
      }
    },
    {
      label: "任务类型",
      prop: "taskCategory",
      width: 120
    },
    {
      label: "任务状态",
      prop: "taskStatus",
      width: 120
    },
    {
      label: "实体触点",
      prop: "entityTouchpoint",
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      label: "用户活动",
      prop: "taskActivity",
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      label: "员工行为",
      prop: "employeeBehavior",
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      label: "工作亮点",
      prop: "workHighlight",
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      label: "员工角色",
      prop: "employeeRole",
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      label: "开始时间",
      prop: "startTime",
      width: 160,
      sortable: "custom",
      component: {
        name: "cl-date-text",
        props: { format: "YYYY-MM-DD HH:mm" }
      }
    },
    {
      label: "结束时间",
      prop: "endTime",
      width: 160,
      sortable: "custom",
      component: {
        name: "cl-date-text",
        props: { format: "YYYY-MM-DD HH:mm" }
      }
    },
    {
      label: "完成时间",
      prop: "completionTime",
      width: 160,
      sortable: "custom",
      component: {
        name: "cl-date-text",
        props: { format: "YYYY-MM-DD HH:mm" }
      }
    },
    {
      type: "op",
      buttons: ["edit", "delete"],
      width: 160
    }
  ]
});

// 时间验证函数
const validateTimeRange = (timeRange, fieldName) => {
  if (timeRange && timeRange.length === 2) {
    const [startTime, endTime] = timeRange;
    if (new Date(startTime) > new Date(endTime)) {
      ElMessage.error(`${fieldName}的开始时间不能大于结束时间`);
      return false;
    }
  }
  return true;
};

// 搜索配置 - 显示最核心的筛选条件
const Search = useSearch({
  items: [
    {
      label: "任务名称",
      prop: "name",
      component: {
        name: "el-input",
        props: {
          clearable: true,
          placeholder: "请输入任务名称"
        }
      }
    },
    {
      label: "任务状态",
      prop: "taskStatus",
      component: {
        name: "cl-select",
        props: {
          clearable: true,
          placeholder: "请选择任务状态",
          options: taskBusinessStatusOptions.map(item => ({
            label: item.label,
            value: item.value
          }))
        }
      }
    },
    {
      label: "任务类型",
      prop: "taskCategory",
      component: {
        name: "cl-select",
        props: {
          clearable: true,
          placeholder: "请选择任务类型",
          options: taskCategoryOptions.map(item => ({
            label: item.label,
            value: item.value
          }))
        }
      }
    },
    {
      label: "场景名称",
      prop: "scenarioName",
      component: {
        name: "el-input",
        props: {
          clearable: true,
          placeholder: "请输入场景名称"
        }
      }
    }
  ]
});

// 高级搜索配置 - 包含更多筛选条件
const AdvSearch = useAdvSearch({
  items: [
    {
      label: "步骤名称",
      prop: "stepName",
      component: {
        name: "el-input",
        props: {
          clearable: true,
          placeholder: "请输入步骤名称"
        }
      }
    },
    {
      label: "员工角色",
      prop: "employeeRole",
      component: {
        name: "el-input",
        props: {
          clearable: true,
          placeholder: "请输入员工角色"
        }
      }
    },
    {
      label: "执行时间",
      prop: "executionTimeRange",
      component: {
        name: "el-date-picker",
        props: {
          type: "datetimerange",
          rangeSeparator: "至",
          startPlaceholder: "开始时间",
          endPlaceholder: "结束时间",
          format: "YYYY-MM-DD HH:mm:ss",
          valueFormat: "YYYY-MM-DD HH:mm:ss",
          clearable: true
        },
        on: {
          change: (value) => validateTimeRange(value, "执行时间")
        }
      }
    },
    {
      label: "完成时间",
      prop: "completionTimeRange",
      component: {
        name: "el-date-picker",
        props: {
          type: "datetimerange",
          rangeSeparator: "至",
          startPlaceholder: "完成开始时间",
          endPlaceholder: "完成结束时间",
          format: "YYYY-MM-DD HH:mm:ss",
          valueFormat: "YYYY-MM-DD HH:mm:ss",
          clearable: true
        },
        on: {
          change: (value) => validateTimeRange(value, "完成时间")
        }
      }
    }
  ]
});

// 响应式数据
const scenarioOptions = ref([]);
const stepOptions = ref([]);
const selectedScenarioId = ref(null);

// 加载场景选项
const loadScenarioOptions = async () => {
	try {
		const res = await service.sop.s.o.p.scenario.list({ status: 1 });
		scenarioOptions.value = res.map(item => ({
			label: item.scenarioName,
			value: item.id
		}));
	} catch (error) {
		console.error('加载场景选项失败:', error);
	}
};

// 加载步骤选项
const loadStepOptions = async (scenarioId) => {
	if (!scenarioId) {
		stepOptions.value = [];
		return;
	}
	try {
		const res = await service.sop.s.o.p.step.page({
			sopId: scenarioId,  // 使用sopId参数
			size: 100,
			page: 1
		});
		console.log('步骤数据:', res);
		stepOptions.value = res.list.map(item => ({
			label: item.stepName,
			value: item.id
		}));
		console.log('步骤选项:', stepOptions.value);
	} catch (error) {
		console.error('加载步骤选项失败:', error);
		stepOptions.value = [];
	}
};

// 场景变化时重新加载步骤
const onScenarioChange = (scenarioId) => {
	selectedScenarioId.value = scenarioId;

	// 清空步骤相关字段
	const { form } = Upsert.value || {};
	if (form) {
		form.stepId = null;
		form.stepCode = '';
		form.stepName = '';
	}

	// 重新加载步骤选项
	loadStepOptions(scenarioId);
};

// 步骤变化时的处理
const onStepChange = (stepId) => {
	// 这里可以添加步骤变化时的逻辑
	console.log('步骤变化:', stepId);
};

// 表单配置 - 使用Tab页分组
const Upsert = useUpsert({
	items: [
		// Tab页配置
		{
			type: "tabs",
			props: {
				type: "card",
				labels: [
					{
						label: "基本信息",
						value: "basic"
					},
					{
						label: "关联信息",
						value: "relation"
					},
					{
						label: "执行配置",
						value: "execution"
					},
					{
						label: "调度配置",
						value: "schedule"
					}
				]
			}
		},
		// 基本信息组
		{
			group: "basic",
			label: "任务名称",
			prop: "name",
			component: {
				name: "el-input",
				props: {
					clearable: true,
					placeholder: "请输入任务名称"
				}
			},
			span: 24,
			required: true,
		},
		{
			group: "basic",
			label: "任务描述",
			prop: "description",
			component: {
				name: "el-input",
				props: {
					type: "textarea",
					rows: 3,
					clearable: true,
					placeholder: "请输入任务描述"
				}
			},
			span: 24,
		},
		{
			group: "basic",
			label: "任务类别",
			prop: "taskCategory",
			component: {
				name: "cl-select",
				props: {
					clearable: true,
					placeholder: "请选择任务类别",
					options: taskCategoryOptions.map(item => ({
						label: item.label,
						value: item.value
					}))
				}
			},
			span: 12,
		},
		{
			group: "basic",
			label: "任务状态",
			prop: "taskStatus",
			component: {
				name: "cl-select",
				props: {
					clearable: true,
					placeholder: "请选择任务状态",
					options: taskBusinessStatusOptions.map(item => ({
						label: item.label,
						value: item.value
					}))
				}
			},
			span: 12,
		},
		{
			group: "basic",
			label: "员工角色",
			prop: "employeeRole",
			component: {
				name: "el-input",
				props: {
					clearable: true,
					placeholder: "请输入员工角色"
				}
			},
			span: 12,
		},
		{
			group: "basic",
			label: "任务备注",
			prop: "remark",
			component: {
				name: "el-input",
				props: {
					type: "textarea",
					rows: 2,
					clearable: true,
					placeholder: "请输入任务备注"
				}
			},
			span: 24,
		},
		// 关联信息组
		{
			group: "relation",
			label: "关联场景",
			prop: "scenarioId",
			component: {
				name: "cl-select",
				props: {
					clearable: true,
					placeholder: "请选择关联场景",
					options: () => scenarioOptions.value,
					onChange: onScenarioChange
				}
			},
			span: 12,
		},
		{
			group: "relation",
			label: "场景编号",
			prop: "scenarioCode",
			component: {
				name: "el-input",
				props: {
					placeholder: "场景编号（自动填充）",
					readonly: true,
					class: "readonly-field"
				}
			},
			span: 12,
		},
		{
			group: "relation",
			label: "场景名称",
			prop: "scenarioName",
			component: {
				name: "el-input",
				props: {
					placeholder: "场景名称（自动填充）",
					readonly: true,
					class: "readonly-field"
				}
			},
			span: 12,
		},
		{
			group: "relation",
			label: "关联步骤",
			prop: "stepId",
			component: {
				name: "cl-select",
				props: {
					clearable: true,
					placeholder: "请先选择场景",
					options: () => stepOptions.value,
					onChange: onStepChange
				}
			},
			span: 12,
		},
		{
			group: "relation",
			label: "步骤编号",
			prop: "stepCode",
			component: {
				name: "el-input",
				props: {
					placeholder: "步骤编号（自动填充）",
					readonly: true,
					class: "readonly-field"
				}
			},
			span: 12,
		},
		{
			group: "relation",
			label: "步骤名称",
			prop: "stepName",
			component: {
				name: "el-input",
				props: {
					placeholder: "步骤名称（自动填充）",
					readonly: true,
					class: "readonly-field"
				}
			},
			span: 12,
		},
		// 执行配置组
		{
			group: "execution",
			label: "实体触点",
			prop: "entityTouchpoint",
			component: {
				name: "el-input",
				props: {
					type: "textarea",
					rows: 2,
					clearable: true,
					placeholder: "请输入实体触点"
				}
			},
			span: 24,
		},
		{
			group: "execution",
			label: "任务活动",
			prop: "taskActivity",
			component: {
				name: "el-input",
				props: {
					type: "textarea",
					rows: 2,
					clearable: true,
					placeholder: "请输入任务活动"
				}
			},
			span: 24,
		},
		{
			group: "execution",
			label: "员工行为",
			prop: "employeeBehavior",
			component: {
				name: "el-input",
				props: {
					type: "textarea",
					rows: 2,
					clearable: true,
					placeholder: "请输入员工行为"
				}
			},
			span: 24,
		},
		{
			group: "execution",
			label: "工作亮点",
			prop: "workHighlight",
			component: {
				name: "el-input",
				props: {
					type: "textarea",
					rows: 2,
					clearable: true,
					placeholder: "请输入工作亮点"
				}
			},
			span: 24,
		},
		{
			group: "execution",
			label: "是否需要拍照",
			prop: "photoRequired",
			component: {
				name: "cl-select",
				props: {
					clearable: true,
					placeholder: "请选择是否需要拍照",
					options: yesNoBoolOptions.map(item => ({
						label: item.label,
						value: item.value
					}))
				}
			},
			span: 12,
		},
		{
			group: "execution",
			label: "是否需要附件",
			prop: "attachmentRequired",
			component: {
				name: "cl-select",
				props: {
					clearable: true,
					placeholder: "请选择是否需要附件",
					options: yesNoBoolOptions.map(item => ({
						label: item.label,
						value: item.value
					}))
				}
			},
			span: 12,
		},
		// 调度配置组
		{
			group: "schedule",
			label: "调度状态",
			prop: "scheduleStatus",
			component: {
				name: "cl-select",
				props: {
					clearable: true,
					placeholder: "请选择调度状态",
					options: taskScheduleStatusOptions.map(item => ({
						label: item.label,
						value: item.value
					}))
				}
			},
			span: 12,
			required: true,
		},
		{
			group: "schedule",
			label: "任务来源",
			prop: "type",
			component: {
				name: "cl-select",
				props: {
					clearable: true,
					placeholder: "请选择任务来源",
					options: taskSourceOptions.map(item => ({
						label: item.label,
						value: item.value
					}))
				}
			},
			span: 12,
		},
		{
			group: "schedule",
			label: "最大执行次数",
			prop: "repeatCount",
			component: {
				name: "el-input-number",
				props: {
					min: 0,
					placeholder: "不填为无限次"
				}
			},
			span: 12,
		},
		{
			group: "schedule",
			label: "执行间隔(毫秒)",
			prop: "every",
			component: {
				name: "el-input-number",
				props: {
					min: 1000,
					step: 1000,
					placeholder: "如果设置了cron表达式则此项无效"
				}
			},
			span: 12,
		},
		{
			group: "schedule",
			label: "Cron表达式",
			prop: "cron",
			component: {
				name: "el-input",
				props: {
					clearable: true,
					placeholder: "例如: 0 0 12 * * ? (每天12点执行)"
				}
			},
			span: 24,
		},
		{
			group: "schedule",
			label: "开始时间",
			prop: "startDate",
			component: {
				name: "el-date-picker",
				props: {
					type: "datetime",
					valueFormat: "YYYY-MM-DD HH:mm:ss",
					placeholder: "请选择开始时间"
				}
			},
			span: 12,
		},
		{
			group: "schedule",
			label: "结束时间",
			prop: "endDate",
			component: {
				name: "el-date-picker",
				props: {
					type: "datetime",
					valueFormat: "YYYY-MM-DD HH:mm:ss",
					placeholder: "请选择结束时间"
				}
			},
			span: 12,
		},
		{
			group: "schedule",
			label: "完成时间",
			prop: "completionTime",
			component: {
				name: "el-date-picker",
				props: {
					type: "datetime",
					valueFormat: "YYYY-MM-DD HH:mm:ss",
					placeholder: "请选择完成时间"
				}
			},
			span: 12,
		},
		{
			group: "schedule",
			label: "调度类型",
			prop: "scheduleType",
			component: {
				name: "cl-select",
				props: {
					clearable: true,
					placeholder: "请选择调度类型",
					options: [
						{ label: "Cron表达式", value: 0 },
						{ label: "时间间隔", value: 1 }
					]
				}
			},
			span: 12,
		},
		{
			group: "schedule",
			label: "服务实例名称",
			prop: "service",
			component: {
				name: "el-input",
				props: {
					clearable: true,
					placeholder: "请输入服务实例名称"
				}
			},
			span: 12,
		},
		{
			group: "schedule",
			label: "任务参数数据",
			prop: "data",
			component: {
				name: "el-input",
				props: {
					type: "textarea",
					rows: 3,
					clearable: true,
					placeholder: "请输入任务参数数据(JSON格式)"
				}
			},
			span: 24,
		}
	],

	// 钩子函数
	onOpen: () => {
		// 打开表单时加载场景选项
		loadScenarioOptions();
	},

	onOpened: (data) => {
		// 表单数据加载完成后，如果有场景ID，则加载对应的步骤选项
		if (data && data.scenarioId) {
			loadStepOptions(data.scenarioId);
		}
	},

	onSubmit: (data, { next }) => {
		// 提交前的数据处理
		next(data);
	}
});

// 页面加载
const onLoad = ({ ctx, app }) => {
  ctx.service(service.task.info).done()
  app.refresh()
}

// 部门筛选处理
const onDepartmentFilterChange = (departmentIds) => {
	console.log('部门筛选变更:', departmentIds);
	
	// 更新选中的部门列表
	selectedDepartments.value = departmentIds;
	
	// 刷新表格
	Table.value?.refresh();
};

// 监听表单数据变化，实现场景和步骤的联动
const { form } = Upsert.value || {};

// 监听场景选择变化
watch(() => form?.scenarioId, async (newScenarioId) => {
	if (newScenarioId && scenarioOptions.value.length > 0) {
		// 根据选择的场景ID获取场景信息
		const selectedScenario = scenarioOptions.value.find(item => item.value === newScenarioId);
		if (selectedScenario) {
			// 自动填充场景相关信息
			try {
				const scenarioInfo = await service.sop.s.o.p.scenario.info({ id: newScenarioId });
				if (form) {
					form.scenarioCode = scenarioInfo.scenarioCode;
					form.scenarioName = scenarioInfo.scenarioName;
				}
			} catch (error) {
				console.error('获取场景信息失败:', error);
			}
		}

		// 加载对应的步骤选项
		await loadStepOptions(newScenarioId);

		// 清空之前选择的步骤
		if (form) {
			form.stepId = null;
			form.stepCode = '';
			form.stepName = '';
		}
	}
}, { immediate: false });

// 监听步骤选择变化
watch(() => form?.stepId, async (newStepId) => {
	if (newStepId && stepOptions.value.length > 0) {
		// 根据选择的步骤ID获取步骤信息
		try {
			const stepInfo = await service.sop.s.o.p.step.info({ id: newStepId });
			if (form) {
				form.stepCode = stepInfo.stepCode;
				form.stepName = stepInfo.stepName;
			}
		} catch (error) {
			console.error('获取步骤信息失败:', error);
		}
	}
}, { immediate: false });
</script>

<style scoped>
/* 自定义黄色标签样式 - 用于已关闭状态 */
.custom-yellow-tag {
  background-color: #ffeb3b !important;
  color: #333 !important;
  border-color: #ffeb3b !important;
}

.custom-yellow-tag.el-tag--warning {
  background-color: #ffeb3b !important;
  color: #333 !important;
  border-color: #ffeb3b !important;
}

/* 只读字段样式 - 支持主题适配 */
:deep(.readonly-field .el-input__wrapper) {
  background-color: var(--el-fill-color-light) !important;
  cursor: not-allowed;
}

:deep(.readonly-field .el-input__inner) {
  cursor: not-allowed;
  color: var(--el-text-color-regular) !important;
}

/* 暗色主题适配 */
html.dark :deep(.readonly-field .el-input__wrapper) {
  background-color: var(--el-fill-color-darker) !important;
}

html.dark :deep(.readonly-field .el-input__inner) {
  color: var(--el-text-color-primary) !important;
}
</style>
