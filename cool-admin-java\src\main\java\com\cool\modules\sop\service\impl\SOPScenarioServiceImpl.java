package com.cool.modules.sop.service.impl;

import com.cool.core.base.BaseServiceImpl;
import com.cool.core.exception.CoolException;
import com.cool.core.exception.CoolPreconditions;
import com.cool.modules.sop.entity.SOPScenarioEntity;
import com.cool.modules.sop.entity.SOPStepEntity;
import com.cool.modules.sop.mapper.SOPScenarioMapper;
import com.cool.modules.sop.mapper.SOPStepMapper;
import com.cool.modules.sop.service.SOPScenarioService;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.enums.*;
import com.cool.modules.task.service.TaskInfoService;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.json.JSONUtil;
import cn.hutool.json.JSONObject;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.ArrayList;

/**
 * SOP场景服务实现
 */
@Slf4j
@Service
public class SOPScenarioServiceImpl extends BaseServiceImpl<SOPScenarioMapper, SOPScenarioEntity> implements SOPScenarioService {

    @Autowired
    private SOPStepMapper sopStepMapper;

    @Lazy
    @Autowired
    private TaskInfoService taskInfoService;

    @Override
    public List<SOPScenarioEntity> getByIndustry(String industryCode) {
        QueryWrapper wrapper = QueryWrapper.create()
                .eq("industry_id", industryCode)
                .eq("status", 1)
                .orderBy("create_time", false);
        return list(wrapper);
    }

    @Override
    public List<SOPScenarioEntity> getByPhase(String phaseCode) {
        QueryWrapper wrapper = QueryWrapper.create()
                .eq("stage", phaseCode)
                .eq("status", 1)
                .orderBy("create_time", false);
        return list(wrapper);
    }

    @Override
    @Transactional
    public SOPScenarioEntity copyScenario(Long id, String newScenarioName, String newScenarioCode) {
        SOPScenarioEntity original = getById(id);
        CoolPreconditions.check(original != null, "原SOP场景不存在");

        // 检查新场景编码是否重复
        QueryWrapper wrapper = QueryWrapper.create()
                .eq("scenario_code", newScenarioCode);
        long count = count(wrapper);
        CoolPreconditions.check(count == 0, "场景编码已存在");

        // 复制场景
        SOPScenarioEntity newScenario = new SOPScenarioEntity();
        // 复制所有字段
        newScenario.setIndustryId(original.getIndustryId());
        newScenario.setIndustryName(original.getIndustryName());
        newScenario.setStage(original.getStage());
        newScenario.setModuleCode(original.getModuleCode());
        newScenario.setModuleName(original.getModuleName());
        newScenario.setScenarioCode(newScenarioCode);
        newScenario.setScenarioName(newScenarioName);
        newScenario.setExecutionCycle(original.getExecutionCycle());
        newScenario.setExecutionFrequency(original.getExecutionFrequency());
        newScenario.setExecutionCount(original.getExecutionCount());
        newScenario.setDescription(original.getDescription());
        newScenario.setTotalSteps(original.getTotalSteps());
        newScenario.setEstimatedDuration(original.getEstimatedDuration());
        newScenario.setDifficultyLevel(original.getDifficultyLevel());
        newScenario.setQualityStandard(original.getQualityStandard());
        newScenario.setSuccessCriteria(original.getSuccessCriteria());
        newScenario.setRiskPoints(original.getRiskPoints());
        newScenario.setAttentionPoints(original.getAttentionPoints());
        newScenario.setApplicableArea(original.getApplicableArea());
        newScenario.setVersion("1.0");
        newScenario.setStatus(0); // 草稿状态

        save(newScenario);
        log.info("复制SOP场景成功，原ID: {}, 新ID: {}", id, newScenario.getId());
        return newScenario;
    }

    @Override
    @Transactional
    public void publishScenario(Long id) {
        SOPScenarioEntity scenario = getById(id);
        CoolPreconditions.check(scenario != null, "SOP场景不存在");
        CoolPreconditions.check(scenario.getStatus() != 1, "SOP场景已发布");

        scenario.setStatus(1);
        scenario.setUpdateTime(new Date());
        updateById(scenario);
        log.info("发布SOP场景成功，ID: {}", id);
    }

    @Override
    @Transactional
    public void archiveScenario(Long id) {
        SOPScenarioEntity scenario = getById(id);
        CoolPreconditions.check(scenario != null, "SOP场景不存在");

        scenario.setStatus(2);
        scenario.setUpdateTime(new Date());
        updateById(scenario);
        log.info("归档SOP场景成功，ID: {}", id);
    }

    @Override
    @Transactional
    public void updateVersion(Long id, String version) {
        SOPScenarioEntity scenario = getById(id);
        CoolPreconditions.check(scenario != null, "SOP场景不存在");

        scenario.setVersion(version);
        scenario.setUpdateTime(new Date());
        updateById(scenario);
        log.info("更新SOP场景版本成功，ID: {}, 版本: {}", id, version);
    }

    @Override
    @Transactional
    public void convertExecutionCycleByAI(Long id) {
        SOPScenarioEntity scenario = getById(id);
        CoolPreconditions.check(scenario != null, "SOP场景不存在");
        CoolPreconditions.check(scenario.getExecutionCycle() != null && !scenario.getExecutionCycle().trim().isEmpty(), 
                "执行周期文本不能为空");

        try {
            // 使用简单的规则转换（移除AI依赖）
            String cycleText = scenario.getExecutionCycle().toLowerCase();
            if (cycleText.contains("每日") || cycleText.contains("日") || cycleText.contains("天")) {
                scenario.setExecutionFrequency("daily");
                scenario.setExecutionCount(1);
            } else if (cycleText.contains("每周") || cycleText.contains("周")) {
                scenario.setExecutionFrequency("weekly");
                scenario.setExecutionCount(1);
            } else if (cycleText.contains("每月") || cycleText.contains("月")) {
                scenario.setExecutionFrequency("monthly");
                scenario.setExecutionCount(1);
            } else {
                scenario.setExecutionFrequency("custom");
                scenario.setExecutionCount(1);
            }

            scenario.setUpdateTime(new Date());
            updateById(scenario);
            log.info("转换执行周期成功，ID: {}", id);
        } catch (Exception e) {
            log.error("转换执行周期失败，ID: {}, 错误: {}", id, e.getMessage());
            throw new RuntimeException("转换执行周期失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getScenarioStats(String industryCode) {
        Map<String, Object> stats = new HashMap<>();
        
        QueryWrapper baseWrapper = QueryWrapper.create();
        if (industryCode != null && !industryCode.trim().isEmpty()) {
            baseWrapper.eq("industry_id", industryCode);
        }

        // 总数
        long totalCount = count(baseWrapper);
        stats.put("totalCount", totalCount);

        // 按状态统计
        Map<String, Long> statusStats = new HashMap<>();
        statusStats.put("draft", count(baseWrapper.clone().eq("status", 0)));
        statusStats.put("published", count(baseWrapper.clone().eq("status", 1)));
        statusStats.put("archived", count(baseWrapper.clone().eq("status", 2)));
        stats.put("statusStats", statusStats);

        // 按难度级别统计
        Map<String, Long> difficultyStats = new HashMap<>();
        difficultyStats.put("easy", count(baseWrapper.clone().eq("difficulty_level", 1)));
        difficultyStats.put("medium", count(baseWrapper.clone().eq("difficulty_level", 2)));
        difficultyStats.put("hard", count(baseWrapper.clone().eq("difficulty_level", 3)));
        stats.put("difficultyStats", difficultyStats);

        return stats;
    }

    @Override
    @Transactional
    public void generateTasksFromScenario(Long scenarioId) {
        SOPScenarioEntity scenario = getById(scenarioId);
        CoolPreconditions.check(scenario != null, "SOP场景不存在");

        try {
            // 获取场景关联的步骤
            QueryWrapper stepWrapper = QueryWrapper.create()
                    .eq("scenario_code", scenario.getScenarioCode())
                    .eq("status", 1)
                    .orderBy("step_order", true);
            List<SOPStepEntity> steps = sopStepMapper.selectListByQuery(stepWrapper);

            if (steps.isEmpty()) {
                log.warn("场景{}暂无关联的步骤，无法生成任务", scenarioId);
                return;
            }

            // 为每个步骤创建任务
            int taskOrder = 1;
            for (SOPStepEntity step : steps) {
                TaskInfoEntity task = new TaskInfoEntity();

                // 基本信息
                task.setName(step.getStepName());
                task.setDescription(step.getStepDescription());
                task.setTaskStatus(TaskBusinessStatusEnum.PENDING_ASSIGN.getCode());
                task.setTaskCategory(TaskCategoryEnum.SOP_STEP.getCode());
                task.setCreateTime(new Date());

                // 场景信息
                task.setScenarioId(scenarioId);
                task.setScenarioCode(scenario.getScenarioCode());
                task.setScenarioName(scenario.getScenarioName());

                // 步骤信息
                task.setStepId(step.getId());
                task.setStepCode(step.getStepCode());
                task.setStepName(step.getStepName());
                task.setEntityTouchpoint(step.getEntityTouchpoint());
                task.setTaskActivity(step.getUserActivity());
                task.setEmployeeBehavior(step.getEmployeeBehavior());
                task.setWorkHighlight(step.getWorkHighlight());
                task.setEmployeeRole(step.getEmployeeRole());

                // 执行要求
                task.setPhotoRequired(isPhotoRequired(step));
                task.setAttachmentRequired(isAttachmentRequired(step));

                // 任务调度相关（设置为手动任务）
                task.setScheduleStatus(TaskScheduleStatusEnum.STOPPED.getCode()); // 停止调度
                task.setType(TaskSourceEnum.USER.getCode()); // 用户创建
                task.setScheduleType(0); // 0:cron类型

                // 备注信息
                StringBuilder remark = new StringBuilder();
                remark.append("基于SOP场景自动生成 - 场景: ").append(scenario.getScenarioName());
                if (step.getRiskWarnings() != null && !step.getRiskWarnings().trim().isEmpty()) {
                    remark.append("\n风险提醒: ").append(step.getRiskWarnings());
                }
                if (step.getSkillRequirements() != null && !step.getSkillRequirements().trim().isEmpty()) {
                    remark.append("\n技能要求: ").append(step.getSkillRequirements());
                }
                if (step.getQualityCheckPoints() != null && !step.getQualityCheckPoints().trim().isEmpty()) {
                    remark.append("\n质量检查: ").append(step.getQualityCheckPoints());
                }
                if (step.getWorkHighlight() != null && !step.getWorkHighlight().trim().isEmpty()) {
                    remark.append("\n工作亮点: ").append(step.getWorkHighlight());
                }
                if (step.getEmployeeRole() != null && !step.getEmployeeRole().trim().isEmpty()) {
                    remark.append("\n员工角色: ").append(step.getEmployeeRole());
                }
                task.setRemark(remark.toString());

                taskInfoService.save(task);
                taskOrder++;
            }

            log.info("从场景{}生成{}个任务", scenarioId, steps.size());
        } catch (Exception e) {
            log.error("从场景生成任务失败，场景ID: {}, 错误: {}", scenarioId, e.getMessage());
            throw new CoolException("生成任务失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Map<String, Object> generateTasksByAI(String userInput) {
        log.info("开始AI任务生成，用户输入: {}", userInput);

        try {
            // 获取所有可用场景
            List<SOPScenarioEntity> allScenarios = list(QueryWrapper.create().eq("status", 1));
            
            if (allScenarios.isEmpty()) {
                throw new CoolException("系统中没有可用的SOP场景");
            }

            // 简单的场景匹配逻辑（不依赖AI服务）
            SOPScenarioEntity bestMatch = findBestMatchingScenario(userInput, allScenarios);
            
            if (bestMatch == null) {
                throw new CoolException("未找到匹配的SOP场景，请尝试更具体的描述");
            }

            // 生成任务
            generateTasksFromScenario(bestMatch.getId());

            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "任务生成成功");
            response.put("scenarioId", bestMatch.getId());
            response.put("scenarioName", bestMatch.getScenarioName());
            response.put("scenarioCode", bestMatch.getScenarioCode());
            response.put("tasksGenerated", getTaskCountForScenario(bestMatch.getId()));

            log.info("AI任务生成完成，场景: {}, 任务数: {}", bestMatch.getScenarioName(), response.get("tasksGenerated"));
            return response;

        } catch (Exception e) {
            log.error("AI任务生成失败: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 简单的场景匹配算法
     */
    private SOPScenarioEntity findBestMatchingScenario(String userInput, List<SOPScenarioEntity> scenarios) {
        String input = userInput.toLowerCase();
        SOPScenarioEntity bestMatch = null;
        double bestScore = 0.0;

        for (SOPScenarioEntity scenario : scenarios) {
            double score = 0.0;
            String scenarioName = scenario.getScenarioName().toLowerCase();
            String description = scenario.getDescription() != null ? scenario.getDescription().toLowerCase() : "";

            // 关键词匹配
            if (input.contains(scenarioName) || scenarioName.contains(input)) {
                score += 0.8;
            }

            // 描述匹配
            String[] words = input.split("\\s+");
            for (String word : words) {
                if (word.length() > 2) {
                    if (scenarioName.contains(word)) {
                        score += 0.3;
                    }
                    if (description.contains(word)) {
                        score += 0.2;
                    }
                }
            }

            if (score > bestScore) {
                bestScore = score;
                bestMatch = scenario;
            }
        }

        return bestScore > 0.5 ? bestMatch : null;
    }

    private int getTaskCountForScenario(Long scenarioId) {
        SOPScenarioEntity scenario = getById(scenarioId);
        if (scenario != null) {
            return scenario.getTotalSteps() != null ? scenario.getTotalSteps() : 0;
        }
        return 0;
    }

    @Override
    public String getScenariosAsJsonContext() {
        List<SOPScenarioEntity> scenarios = list(QueryWrapper.create().eq("status", 1));
        return JSONUtil.toJsonStr(scenarios.stream()
                .map(scenario -> {
                    JSONObject obj = new JSONObject();
                    obj.put("id", scenario.getId());
                    obj.put("name", scenario.getScenarioName());
                    obj.put("code", scenario.getScenarioCode());
                    obj.put("description", scenario.getDescription());
                    return obj;
                })
                .collect(Collectors.toList()));
    }

    @Override
    public List<Map<String, Object>> getSmartScenarioSuggestions(String description) {
        log.info("根据用户输入获取智能场景建议: {}", description);
        
        try {
            // 获取所有已发布的场景
            List<SOPScenarioEntity> allScenarios = list(QueryWrapper.create().eq("status", 1));
            
            if (allScenarios.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 智能匹配算法
            List<ScenarioMatch> matches = new ArrayList<>();
            String inputLower = description.toLowerCase();
            
            for (SOPScenarioEntity scenario : allScenarios) {
                double score = calculateMatchScore(inputLower, scenario);
                if (score > 0.1) { // 设置最低匹配阈值
                    matches.add(new ScenarioMatch(scenario, score));
                }
            }
            
            // 按匹配度排序，取前10个
            matches.sort((a, b) -> Double.compare(b.score, a.score));
            
            return matches.stream()
                    .limit(10)
                    .map(match -> {
                        Map<String, Object> suggestion = new HashMap<>();
                        SOPScenarioEntity scenario = match.scenario;
                        suggestion.put("id", scenario.getId());
                        suggestion.put("name", scenario.getScenarioName());
                        suggestion.put("code", scenario.getScenarioCode());
                        suggestion.put("description", scenario.getDescription());
                        suggestion.put("industryName", scenario.getIndustryName());
                        suggestion.put("moduleName", scenario.getModuleName());
                        suggestion.put("totalSteps", scenario.getTotalSteps());
                        suggestion.put("estimatedDuration", scenario.getEstimatedDuration());
                        suggestion.put("difficultyLevel", scenario.getDifficultyLevel());
                        suggestion.put("matchScore", Math.round(match.score * 100) / 100.0);
                        return suggestion;
                    })
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("获取智能场景建议失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 计算场景匹配分数
     */
    private double calculateMatchScore(String input, SOPScenarioEntity scenario) {
        double score = 0.0;
        
        String scenarioName = scenario.getScenarioName() != null ? scenario.getScenarioName().toLowerCase() : "";
        String description = scenario.getDescription() != null ? scenario.getDescription().toLowerCase() : "";
        String industryName = scenario.getIndustryName() != null ? scenario.getIndustryName().toLowerCase() : "";
        String moduleName = scenario.getModuleName() != null ? scenario.getModuleName().toLowerCase() : "";
        
        // 1. 场景名称完全匹配 (权重: 2.0)
        if (input.equals(scenarioName)) {
            score += 2.0;
        }
        
        // 2. 场景名称包含关系 (权重: 1.5)
        if (scenarioName.contains(input) || input.contains(scenarioName)) {
            score += 1.5;
        }
        
        // 3. 描述匹配 (权重: 1.0)
        if (description.contains(input) || input.contains(description)) {
            score += 1.0;
        }
        
        // 4. 行业匹配 (权重: 0.8)
        if (industryName.contains(input) || input.contains(industryName)) {
            score += 0.8;
        }
        
        // 5. 模块匹配 (权重: 0.6)
        if (moduleName.contains(input) || input.contains(moduleName)) {
            score += 0.6;
        }
        
        // 6. 关键词匹配 (权重: 0.4)
        String[] inputWords = input.split("\\s+");
        for (String word : inputWords) {
            if (word.length() > 1) {
                if (scenarioName.contains(word)) score += 0.4;
                if (description.contains(word)) score += 0.3;
                if (industryName.contains(word)) score += 0.2;
                if (moduleName.contains(word)) score += 0.1;
            }
        }
        
        // 7. 语义相似度增强 (基于常见业务词汇)
        score += calculateSemanticSimilarity(input, scenarioName, description);
        
        return score;
    }
    
    /**
     * 计算语义相似度
     */
    private double calculateSemanticSimilarity(String input, String scenarioName, String description) {
        double similarity = 0.0;
        
        // 定义业务领域词汇映射
        Map<String, String[]> businessTerms = new HashMap<>();
        businessTerms.put("客户", new String[]{"用户", "顾客", "客户端", "消费者"});
        businessTerms.put("订单", new String[]{"单据", "工单", "任务单", "申请单"});
        businessTerms.put("库存", new String[]{"仓库", "存储", "物料", "货物"});
        businessTerms.put("质量", new String[]{"品质", "检验", "检测", "标准"});
        businessTerms.put("生产", new String[]{"制造", "加工", "生产线", "工艺"});
        businessTerms.put("销售", new String[]{"营销", "推广", "业务", "商务"});
        businessTerms.put("维护", new String[]{"保养", "维修", "检修", "巡检"});
        businessTerms.put("培训", new String[]{"教育", "学习", "指导", "培养"});
        
        for (Map.Entry<String, String[]> entry : businessTerms.entrySet()) {
            String key = entry.getKey();
            String[] synonyms = entry.getValue();
            
            boolean inputContainsKey = input.contains(key);
            boolean scenarioContainsKey = scenarioName.contains(key) || description.contains(key);
            
            if (inputContainsKey && scenarioContainsKey) {
                similarity += 0.5;
            }
            
            // 检查同义词
            for (String synonym : synonyms) {
                boolean inputContainsSynonym = input.contains(synonym);
                boolean scenarioContainsSynonym = scenarioName.contains(synonym) || description.contains(synonym);
                
                if ((inputContainsKey && scenarioContainsSynonym) || 
                    (inputContainsSynonym && scenarioContainsKey)) {
                    similarity += 0.3;
                }
            }
        }
        
        return similarity;
    }
    
    /**
     * 场景匹配结果内部类
     */
    private static class ScenarioMatch {
        final SOPScenarioEntity scenario;
        final double score;
        
        ScenarioMatch(SOPScenarioEntity scenario, double score) {
            this.scenario = scenario;
            this.score = score;
        }
    }

    /**
     * 判断步骤是否需要拍照
     */
    private boolean isPhotoRequired(SOPStepEntity step) {
        if (step == null) return false;

        // 检查各个字段是否包含拍照相关关键词
        String[] photoKeywords = {"拍照", "照片", "图片", "拍摄", "记录图像", "visual", "photo", "image"};

        return containsKeywords(step.getStepDescription(), photoKeywords) ||
               containsKeywords(step.getQualityCheckPoints(), photoKeywords) ||
               containsKeywords(step.getRiskWarnings(), photoKeywords) ||
               containsKeywords(step.getSkillRequirements(), photoKeywords) ||
               "photo".equals(step.getStepType()) ||
               "visual_check".equals(step.getStepType());
    }

    /**
     * 判断步骤是否需要附件
     */
    private boolean isAttachmentRequired(SOPStepEntity step) {
        if (step == null) return false;

        // 检查各个字段是否包含附件相关关键词
        String[] attachmentKeywords = {"附件", "文档", "报告", "记录", "表格", "清单", "证明", "document", "file", "attachment", "report"};

        return containsKeywords(step.getStepDescription(), attachmentKeywords) ||
               containsKeywords(step.getQualityCheckPoints(), attachmentKeywords) ||
               containsKeywords(step.getSkillRequirements(), attachmentKeywords) ||
               "document".equals(step.getStepType()) ||
               "report".equals(step.getStepType());
    }

    /**
     * 检查文本是否包含指定关键词
     */
    private boolean containsKeywords(String text, String[] keywords) {
        if (text == null || text.trim().isEmpty()) return false;

        String lowerText = text.toLowerCase();
        for (String keyword : keywords) {
            if (lowerText.contains(keyword.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取所有场景标签
     */
    @Override
    public List<Map<String, Object>> getAllScenarioTags() {
        log.info("获取所有场景标签");
        
        try {
            // 获取所有已发布的场景
            List<SOPScenarioEntity> scenarios = list(QueryWrapper.create().eq("status", 1));
            
            return scenarios.stream()
                    .map(scenario -> {
                        Map<String, Object> tag = new HashMap<>();
                        tag.put("id", scenario.getId());
                        tag.put("name", scenario.getScenarioName());
                        tag.put("code", scenario.getScenarioCode());
                        tag.put("description", scenario.getDescription());
                        tag.put("industryName", scenario.getIndustryName());
                        tag.put("moduleName", scenario.getModuleName());
                        tag.put("totalSteps", scenario.getTotalSteps());
                        tag.put("estimatedDuration", scenario.getEstimatedDuration());
                        tag.put("difficultyLevel", scenario.getDifficultyLevel());
                        return tag;
                    })
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("获取场景标签失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 修复所有场景的总步骤数
     */
    @Transactional
    public void fixAllScenarioTotalSteps() {
        log.info("开始修复所有场景的总步骤数");
        
        try {
            List<SOPScenarioEntity> scenarios = list();
            int fixedCount = 0;
            
            for (SOPScenarioEntity scenario : scenarios) {
                // 统计实际步骤数
                QueryWrapper wrapper = QueryWrapper.create().eq("sop_id", scenario.getId());
                long actualStepCount = sopStepMapper.selectCountByQuery(wrapper);
                
                // 如果总步骤数不正确，则更新
                if (scenario.getTotalSteps() == null || scenario.getTotalSteps() != actualStepCount) {
                    scenario.setTotalSteps((int) actualStepCount);
                    updateById(scenario);
                    fixedCount++;
                    log.info("修复场景 {} (ID: {}) 的总步骤数: {} -> {}", 
                        scenario.getScenarioName(), scenario.getId(), 
                        scenario.getTotalSteps(), actualStepCount);
                }
            }
            
            log.info("场景总步骤数修复完成，共修复 {} 个场景", fixedCount);
            
        } catch (Exception e) {
            log.error("修复场景总步骤数失败: {}", e.getMessage(), e);
            throw new RuntimeException("修复场景总步骤数失败: " + e.getMessage());
        }
    }
}