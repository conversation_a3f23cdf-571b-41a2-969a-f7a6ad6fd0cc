package com.cool.modules.sop.mapper;

import com.cool.modules.sop.entity.SOPSchedulePermissionEntity;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 调度权限配置Mapper
 */
@Mapper
public interface SOPSchedulePermissionMapper extends BaseMapper<SOPSchedulePermissionEntity> {

    /**
     * 根据角色ID查询权限列表
     */
    List<SOPSchedulePermissionEntity> selectByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据权限类型查询角色列表
     */
    List<Long> selectRoleIdsByPermissionType(@Param("permissionType") String permissionType);

    /**
     * 检查角色是否有指定权限
     */
    boolean hasPermission(@Param("roleId") Long roleId, @Param("permissionType") String permissionType);

    /**
     * 批量插入权限配置
     */
    int insertBatch(@Param("permissions") List<SOPSchedulePermissionEntity> permissions);

    /**
     * 删除角色的所有权限
     */
    int deleteByRoleId(@Param("roleId") Long roleId);
}
