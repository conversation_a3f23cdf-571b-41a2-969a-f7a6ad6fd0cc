package com.cool.modules.sop.controller.admin;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.core.request.R;
import com.cool.modules.sop.entity.TaskExecuteRecordEntity;
import com.cool.modules.sop.service.TaskExecuteRecordService;
import com.cool.modules.sop.service.AILLMService;
import com.mybatisflex.core.query.QueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletRequest;
import cn.hutool.json.JSONObject;

import java.util.Map;

/**
 * 任务执行记录管理控制器
 */
@Tag(name = "任务执行管理", description = "任务的执行、监控和管理")
@CoolRestController(api = {"add", "delete", "update", "page", "list", "info"})
public class AdminTaskController extends BaseController<TaskExecuteRecordService, TaskExecuteRecordEntity> {

    @Autowired
    private AILLMService aiLLMService;

    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        // Required implementation for BaseController
    }

    protected void init(QueryWrapper queryWrapper, Map<String, Object> params) {
        queryWrapper.like("task_name", params.get("taskName"))
                   .eq("status", params.get("status"))
                   .eq("assignee_id", params.get("assigneeId"))
                   .eq("work_order_id", params.get("workOrderId"))
                   .orderBy("id", false);
    }

    @Operation(summary = "获取AI生成任务的建议场景", description = "基于用户输入获取推荐的SOP场景")
    @PostMapping("/ai-suggest-scenarios")
    public R suggestScenarios(@RequestParam String description) {
        try {
            if (description == null || description.trim().isEmpty()) {
                return R.error("描述不能为空");
            }

            var suggestions = aiLLMService.suggestScenarios(description);
            return R.ok(suggestions);
            
        } catch (Exception e) {
            return R.error("获取场景建议失败：" + e.getMessage());
        }
    }

    @Operation(summary = "开始执行任务")
    @PostMapping("/start")
    public R start(@Parameter(description = "任务ID") @RequestParam Long id) {
        try {
            service.startTask(id);
            return R.ok("任务已开始执行");
        } catch (Exception e) {
            return R.error("开始执行失败：" + e.getMessage());
        }
    }

    @Operation(summary = "暂停任务")
    @PostMapping("/pause")
    public R pause(@Parameter(description = "任务ID") @RequestParam Long id) {
        try {
            service.pauseTask(id);
            return R.ok("任务已暂停");
        } catch (Exception e) {
            return R.error("暂停失败：" + e.getMessage());
        }
    }

    @Operation(summary = "恢复任务")
    @PostMapping("/resume")
    public R resume(@Parameter(description = "任务ID") @RequestParam Long id) {
        try {
            service.resumeTask(id);
            return R.ok("任务已恢复");
        } catch (Exception e) {
            return R.error("恢复失败：" + e.getMessage());
        }
    }

    @Operation(summary = "完成任务")
    @PostMapping("/complete")
    public R complete(@Parameter(description = "任务ID") @RequestParam Long id) {
        try {
            service.completeTask(id);
            return R.ok("任务已完成");
        } catch (Exception e) {
            return R.error("完成失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取任务统计信息")
    @GetMapping("/stats")
    public R getStats(@Parameter(description = "负责人ID") @RequestParam(required = false) Long assigneeId) {
        try {
            var stats = service.getTaskStats(assigneeId);
            return R.ok(stats);
        } catch (Exception e) {
            return R.error("获取统计信息失败：" + e.getMessage());
        }
    }

    @Operation(summary = "根据筛选条件获取任务列表")
    @GetMapping("/filter")
    public R getTasksByFilter(@Parameter(description = "筛选条件") @RequestParam String filter,
                             @Parameter(description = "负责人ID") @RequestParam(required = false) Long assigneeId) {
        try {
            var tasks = service.getTasksByFilter(filter, assigneeId);
            return R.ok(tasks);
        } catch (Exception e) {
            return R.error("获取任务列表失败：" + e.getMessage());
        }
    }

    @Operation(summary = "添加执行日志")
    @PostMapping("/log")
    public R addExecutionLog(@Parameter(description = "任务ID") @RequestParam Long taskId,
                           @Parameter(description = "日志内容") @RequestParam String logContent,
                           @Parameter(description = "日志类型") @RequestParam String logType) {
        try {
            service.addExecutionLog(taskId, logContent, logType);
            return R.ok("日志添加成功");
        } catch (Exception e) {
            return R.error("添加日志失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取任务执行日志")
    @GetMapping("/logs/{taskId}")
    public R getExecutionLogs(@Parameter(description = "任务ID") @PathVariable Long taskId) {
        try {
            var logs = service.getExecutionLogs(taskId);
            return R.ok(logs);
        } catch (Exception e) {
            return R.error("获取执行日志失败：" + e.getMessage());
        }
    }

    @Operation(summary = "任务质量检查")
    @PostMapping("/quality-check")
    public R qualityCheck(@Parameter(description = "任务ID") @RequestParam Long taskId,
                         @RequestBody Map<String, Object> checkResult) {
        try {
            var result = service.qualityCheck(taskId, checkResult);
            return R.ok(result);
        } catch (Exception e) {
            return R.error("质量检查失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取AI执行指导")
    @PostMapping("/ai-guidance")
    public R getAIGuidance(@Parameter(description = "任务ID") @RequestParam Long taskId,
                          @Parameter(description = "当前情况描述") @RequestParam(required = false) String currentSituation) {
        try {
            var guidance = aiLLMService.getExecutionGuidance(taskId, currentSituation);
            return R.ok(guidance);
        } catch (Exception e) {
            return R.error("获取AI指导失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取任务详情")
    @GetMapping("/detail/{taskId}")
    public R getTaskDetail(@Parameter(description = "任务ID") @PathVariable Long taskId) {
        try {
            var detail = service.getTaskDetail(taskId);
            return R.ok(detail);
        } catch (Exception e) {
            return R.error("获取任务详情失败：" + e.getMessage());
        }
    }

    @Operation(summary = "AI质量智能检查")
    @PostMapping("/ai-quality-check/{taskId}")
    public R aiQualityCheck(@Parameter(description = "任务ID") @PathVariable Long taskId) {
        try {
            // 获取任务详情
            var taskDetail = service.getTaskDetail(taskId);
            
            // 调用AI进行质量检查
            var qualityResult = aiLLMService.qualityCheck(taskId, taskDetail);
            
            // 保存检查结果
            var result = service.qualityCheck(taskId, qualityResult);
            
            return R.ok(result);
        } catch (Exception e) {
            return R.error("AI质量检查失败：" + e.getMessage());
        }
    }
} 