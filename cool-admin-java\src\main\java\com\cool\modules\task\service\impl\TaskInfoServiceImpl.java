package com.cool.modules.task.service.impl;

// 移除静态导入，使用字符串字段名

import java.util.List;
import java.util.stream.Collectors;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import org.quartz.CronTrigger;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.cool.core.base.BaseServiceImpl;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.sop.service.SOPScenarioService;
import com.cool.modules.task.entity.TaskExecutionEntity;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.entity.TaskLogEntity;
import com.cool.modules.task.enums.TaskExecutionStatusEnum;
import static com.cool.modules.task.entity.table.TaskInfoEntityTableDef.TASK_INFO_ENTITY;
import static com.cool.modules.task.entity.table.TaskLogEntityTableDef.TASK_LOG_ENTITY;
import com.cool.modules.task.enums.TaskBusinessStatusEnum;
import com.cool.modules.task.mapper.TaskInfoMapper;
import com.cool.modules.task.service.TaskExecutionService;
import com.cool.modules.task.service.TaskInfoService;
import com.cool.modules.task.service.TaskDepartmentPermissionService;
import com.cool.modules.task.utils.ScheduleUtils;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class TaskInfoServiceImpl extends BaseServiceImpl<TaskInfoMapper, TaskInfoEntity> implements
    TaskInfoService {

    final private Scheduler scheduler;

    @Lazy
    @Autowired
    private SOPScenarioService sopScenarioService;

    @Lazy
    @Autowired
    private TaskExecutionService taskExecutionService;

    @Lazy
    @Autowired
    private TaskInfoService taskInfoService;

    @Lazy
    @Autowired
    private TaskDepartmentPermissionService departmentPermissionService;

    @Override
    public void init() {
        // try {
        //     List<TaskInfoEntity> list = list();
        //     list.forEach(scheduleJob -> {
        //         CronTrigger cronTrigger = ScheduleUtils.getCronTrigger(scheduler,
        //             scheduleJob.getJobId());
        //         if (cronTrigger == null) {
        //             ScheduleUtils.createScheduleJob(scheduler, scheduleJob);
        //         } else {
        //             ScheduleUtils.updateScheduleJob(scheduler, scheduleJob);
        //         }
        //         updateById(scheduleJob);
        //     });
        // } catch (Exception ignored) {
        // }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void once(Long taskId) {
        ScheduleUtils.run(scheduler, getById(taskId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stop(Long taskId) {
        ScheduleUtils.pauseJob(scheduler, taskId + "");
        TaskInfoEntity taskInfoEntity = getById(taskId);
        taskInfoEntity.setScheduleStatus(0);
        updateById(taskInfoEntity);
        modifyAfter(JSONUtil.parseObj(taskInfoEntity), taskInfoEntity);
    }

    @Override
    public Object log(Page page, Long taskId, Integer status) {

        QueryWrapper queryWrapper = QueryWrapper.create().select(TASK_LOG_ENTITY.DETAIL,
                TASK_LOG_ENTITY.STATUS, TASK_LOG_ENTITY.CREATE_TIME,
                TASK_INFO_ENTITY.NAME).from(TASK_LOG_ENTITY)
            .leftJoin(TASK_INFO_ENTITY).on(TASK_LOG_ENTITY.TASK_ID.eq(TASK_INFO_ENTITY.ID))
            .eq(TaskLogEntity::getTaskId, taskId, taskId != null)
            .eq(TaskLogEntity::getStatus, status, status != null)
            .orderBy(TaskLogEntity::getCreateTime, false);
        return mapper.paginateAs(page, queryWrapper, TaskLogEntity.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void start(Long taskId, Integer type) {
        TaskInfoEntity taskInfoEntity = getById(taskId);
        taskInfoEntity.setTaskStatus(TaskBusinessStatusEnum.EXECUTING.getCode());
        // taskInfoEntity.setScheduleStatus(1);
        // if (type != null) {
        //     taskInfoEntity.setScheduleType(type);
        // }
        // boolean isExists = false;
        // try {
        //     isExists = scheduler.checkExists(ScheduleUtils.getJobKey(taskId + ""));
        // } catch (SchedulerException e) {
        //     log.error("err", e);
        // }
        // if (isExists) {
        //     ScheduleUtils.updateScheduleJob(scheduler, taskInfoEntity);
        //     ScheduleUtils.resumeJob(scheduler, taskId + "");
        // } else {
        //     ScheduleUtils.createScheduleJob(scheduler, taskInfoEntity);
        // }
        updateById(taskInfoEntity);
        modifyAfter(JSONUtil.parseObj(taskInfoEntity), taskInfoEntity);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(JSONObject requestParams, TaskInfoEntity scheduleJob) {
        scheduleJob.setScheduleStatus(1);
        super.add(scheduleJob);
        scheduleJob.setJobId(scheduleJob.getId() + "");

        ScheduleUtils.createScheduleJob(scheduler, scheduleJob);
        updateById(scheduleJob);
        super.modifyAfter(requestParams, scheduleJob);
        return scheduleJob.getId();
    }

    @Override
    public List<TaskInfoEntity> list(QueryWrapper queryWrapper) {
        List<TaskInfoEntity> tasks = super.list(queryWrapper);
        return enrichTasksWithExecutions(tasks);
    }

    /**
     * 重写page方法，使用MyBatis关联查询返回包含部门名称的数据
     */
    @Override
    public Object page(JSONObject requestParams, Page<TaskInfoEntity> page, QueryWrapper queryWrapper) {
        try {
            JSONObject adminUserInfo = CoolSecurityUtil.getAdminUserInfo(requestParams);
            Long currentUserId = adminUserInfo.getLong("userId");
            boolean isAdmin = "admin".equals(adminUserInfo.getStr("username"));
            
            // 转换参数格式
            Map<String, Object> params = new HashMap<>();
            if (requestParams != null) {
                params.putAll(requestParams);
            }
            
            // 应用部门权限过滤（如果需要）
            if (!isAdmin && departmentPermissionService != null && currentUserId != null) {
                try {
                    // 获取用户有权限的部门ID列表
                    Long[] userDepartmentIds = departmentPermissionService.getUserDepartmentIds(currentUserId);
                    if (userDepartmentIds != null && userDepartmentIds.length > 0) {
                        params.put("departmentIds", Arrays.asList(userDepartmentIds));
                    }
                } catch (Exception e) {
                    log.warn("应用部门权限过滤失败，跳过权限过滤", e);
                }
            }
            
            // 计算分页参数
            int pageNum = (int) page.getPageNumber();
            int pageSize = (int) page.getPageSize();
            int offset = (pageNum - 1) * pageSize;
            params.put("offset", offset);
            params.put("limit", pageSize);
            
            // 使用关联查询获取包含部门名称的数据
            List<TaskInfoEntity> records = mapper.selectTaskInfoWithDepartment(params);
            int total = mapper.countTaskInfoWithDepartment(params);
            
            // 为每个任务关联执行人信息
            List<TaskInfoEntity> enrichedTasks = enrichTasksWithExecutions(records);
            
            // 构造分页结果
            Page<TaskInfoEntity> resultPage = new Page<>();
            resultPage.setRecords(enrichedTasks);
            resultPage.setTotalRow(total);
            resultPage.setPageNumber(pageNum);
            resultPage.setPageSize(pageSize);
            
            log.info("查询任务信息列表成功 - 总记录数: {}, 当前页: {}, 页大小: {}, 执行人信息已关联", total, pageNum, pageSize);
            return resultPage;
            
        } catch (Exception e) {
            log.error("查询任务信息列表失败", e);
            // 出错时回退到原始方法
            Page<TaskInfoEntity> result = (Page<TaskInfoEntity>) super.page(requestParams, page, queryWrapper);
            
            // 为分页结果中的每个任务关联执行人信息
            List<TaskInfoEntity> enrichedTasks = enrichTasksWithExecutions(result.getRecords());
            result.setRecords(enrichedTasks);
            
            return result;
        }
    }

    /**
     * 为任务列表关联执行人信息
     */
    private List<TaskInfoEntity> enrichTasksWithExecutions(List<TaskInfoEntity> tasks) {
        // 为每个任务关联执行人信息
        for (TaskInfoEntity task : tasks) {
            List<TaskExecutionEntity> executions = taskExecutionService.getByTaskId(task.getId());

            // 设置执行记录列表 - 同时设置两个字段确保兼容性
            task.setExecutions(executions);
            task.setExecutionEntitys(executions);

            if (!executions.isEmpty()) {
                // 构建所有执行人的显示名称
                List<String> assigneeNames = executions.stream()
                    .map(TaskExecutionEntity::getAssigneeName)
                    .collect(Collectors.toList());

                // 设置主要执行人信息（第一个）
                TaskExecutionEntity primaryExecution = executions.get(0);
                task.setAssigneeId(primaryExecution.getAssigneeId());

                // 如果有多个执行人，显示为 "主要执行人 + N人"
                if (assigneeNames.size() == 1) {
                    task.setAssigneeName(assigneeNames.get(0));
                } else {
                    task.setAssigneeName(assigneeNames.get(0) + " +" + (assigneeNames.size() - 1) + "人");
                }
            }
        }

        return tasks;
    }

    @Override
    public boolean update(JSONObject requestParams, TaskInfoEntity entity) {
        updateById(entity);
        ScheduleUtils.deleteScheduleJob(scheduler, entity.getId().toString());
        if (entity.getScheduleStatus() == 1) {
            start(entity.getId(), entity.getScheduleType());
        } else {
            stop(entity.getId());
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(JSONObject requestParams, Long... ids) {
        Convert.toList(String.class, ids).forEach(jobId -> {
            ScheduleUtils.deleteScheduleJob(scheduler, jobId);
        });
        return super.delete(requestParams, ids);
    }

    @Override
    public List<TaskExecutionEntity> getTaskExecutions(Long taskId) {
        return taskExecutionService.getByTaskId(taskId);
    }

    @Override
    public com.mybatisflex.core.paginate.Page<TaskInfoEntity> getPersonalTasksWithExecution(Long assigneeId, Integer page, Integer size, String businessStatus) {
        try {
            // 第一步：获取当前用户的所有执行记录
            List<TaskExecutionEntity> userExecutions = taskExecutionService.list(
                com.mybatisflex.core.query.QueryWrapper.create()
                    .eq("assignee_id", assigneeId)
            );

            if (userExecutions.isEmpty()) {
                return new com.mybatisflex.core.paginate.Page<>(page, size, 0);
            }

            // 根据业务状态筛选执行记录
            if (businessStatus != null && !businessStatus.isEmpty()) {
                userExecutions = userExecutions.stream()
                    .filter(exec -> {
                        String status = exec.getExecutionStatus();
                        if ("pending".equals(businessStatus)) {
                            return TaskExecutionStatusEnum.ASSIGNED.getCode().equals(status) || TaskExecutionStatusEnum.ACCEPTED.getCode().equals(status);
                        } else if ("inProgress".equals(businessStatus)) {
                            return TaskExecutionStatusEnum.IN_PROGRESS.getCode().equals(status);
                        } else if ("completed".equals(businessStatus)) {
                            return TaskExecutionStatusEnum.COMPLETED.getCode().equals(status);
                        }
                        return true;
                    })
                    .collect(Collectors.toList());
            }

            if (userExecutions.isEmpty()) {
                return new com.mybatisflex.core.paginate.Page<>(page, size, 0);
            }

            // 提取任务ID
            List<Long> taskIds = userExecutions.stream()
                .map(TaskExecutionEntity::getTaskId)
                .distinct()
                .collect(Collectors.toList());

            // 第二步：查询这些任务的详细信息
            com.mybatisflex.core.query.QueryWrapper taskQuery = com.mybatisflex.core.query.QueryWrapper.create()
                .eq("id", taskIds.get(0)); // 先查询第一个任务

            // 如果有多个任务ID，使用in查询
            if (taskIds.size() > 1) {
                taskQuery = com.mybatisflex.core.query.QueryWrapper.create()
                    .in("id", taskIds);
            }

            taskQuery.orderBy("create_time", false);

            // 执行分页查询
            com.mybatisflex.core.paginate.Page<TaskInfoEntity> result = mapper.paginate(
                new com.mybatisflex.core.paginate.Page<>(page, size), taskQuery);

            // 第三步：为每个任务填充执行记录信息
            if (result.getRecords() != null && !result.getRecords().isEmpty()) {
                List<TaskInfoEntity> enrichedTasks = enrichTasksWithExecutions(result.getRecords());
                result.setRecords(enrichedTasks);
            }

            return result;
        } catch (Exception e) {
            System.err.println("获取个人任务失败: " + e.getMessage());
            e.printStackTrace();
            return new com.mybatisflex.core.paginate.Page<>(page, size, 0);
        }
    }

    @Override
    public List<TaskInfoEntity> listByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return List.of();
        }
        QueryWrapper queryWrapper = QueryWrapper.create().in("id", ids);
        return super.list(queryWrapper);
    }

    @Transactional
    @Override
    public boolean startTask(Long taskId, Long assigneeId) {
        try {
            // 更新任务执行记录状态为IN_PROGRESS
            // 更新任状态也为IN_PROGRESS
            taskInfoService.start(taskId, null);
            return taskExecutionService.startTask(taskId, assigneeId);
        } catch (Exception e) {
            System.err.println("开始任务失败: " + e.getMessage());
            return false;
        }
    }

    @Transactional
    @Override
    public boolean batchUpdateTaskTime(List<Long> taskIds, String startTime, String endTime) {
        try {
            if (taskIds == null || taskIds.isEmpty()) {
                return false;
            }

            // 批量更新任务时间
            for (Long taskId : taskIds) {
                TaskInfoEntity task = getById(taskId);
                if (task != null) {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    if (startTime != null && !startTime.isEmpty()) {
                        try {
                            task.setStartTime(dateFormat.parse(startTime));
                        } catch (Exception e) {
                            log.warn("解析开始时间失败: {}", startTime);
                        }
                    }
                    if (endTime != null && !endTime.isEmpty()) {
                        try {
                            task.setEndTime(dateFormat.parse(endTime));
                        } catch (Exception e) {
                            log.warn("解析结束时间失败: {}", endTime);
                        }
                    }
                    task.setUpdateTime(new java.util.Date());
                    updateById(task);
                }
            }

            return true;
        } catch (Exception e) {
            log.error("批量更新任务时间失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
