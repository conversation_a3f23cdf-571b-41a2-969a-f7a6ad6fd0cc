<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			
			<!-- 部门筛选器 -->
			<department-filter 
				v-model="selectedDepartments"
				@change="onDepartmentFilterChange"
				style="margin-right: 10px; width: 240px;"
			/>
			
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" @row-click="handleRowClick" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />

		<!-- 任务步骤流侧边栏 -->
		<el-drawer
			v-model="drawerVisible"
			title="任务步骤流程"
			direction="rtl"
			size="80%"
		>
			<TaskStepsFlow
				v-if="drawerVisible && selectedPackage"
				:package-data="selectedPackage"
				:tasks="packageTasks"
				:steps="packageSteps"
				@refresh="handleTaskFlowRefresh"
			/>
		</el-drawer>
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "task-package",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";
import { ref, h } from "vue";
import { ElMessage } from "element-plus";
import TaskStepsFlow from "../components/task-steps-flow.vue";
import DepartmentFilter from "../components/DepartmentFilter.vue";
import DepartmentTag from "../components/DepartmentTag.vue";

const { service } = useCool();
const { t } = useI18n();

// 侧边栏相关数据
const drawerVisible = ref(false);
const selectedPackage = ref<any | null>(null);
const packageTasks = ref<any[]>([]);
const packageSteps = ref<any[]>([]);

// 部门筛选相关
const selectedDepartments = ref([]);

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: "任务包名称",
			prop: "packageName",
			required: true,
			component: { name: "el-input", props: { placeholder: "请输入任务包名称" } },
		},
		{
			label: "场景ID",
			prop: "scenarioId",
			component: { name: "el-input-number", props: { placeholder: "请输入场景ID" } },
		},
		{
			label: "场景名称",
			prop: "scenarioName",
			component: { name: "el-input", props: { placeholder: "请输入场景名称" } },
		},
		{
			label: "场景编码",
			prop: "scenarioCode",
			component: { name: "el-input", props: { placeholder: "请输入场景编码" } },
		},
		{
			label: "描述",
			prop: "description",
			component: {
				name: "el-input",
				props: {
					type: "textarea",
					rows: 3,
					placeholder: "请输入任务包描述"
				}
			},
		},
		{
			label: "状态",
			prop: "packageStatus",
			value: 0,
			component: {
				name: "el-select",
				options: [
					{ label: "待分配", value: 0 },
					{ label: "执行中", value: 1 },
					{ label: "已完成", value: 2 },
					{ label: "已关闭", value: 3 },
				],
			},
		},
		{
			label: "优先级",
			prop: "priority",
			value: 2,
			component: {
				name: "el-select",
				options: [
					{ label: "低", value: 1 },
					{ label: "中", value: 2 },
					{ label: "高", value: 3 },
				],
			},
		},
	],
});

// cl-table
const Table = useTable({
	onRefresh: (params, { next }) => {
		// 应用部门筛选参数
		if (selectedDepartments.value && selectedDepartments.value.length > 0) {
			params.departmentIds = selectedDepartments.value;
		}
		next(params);
	},
	columns: [
		{ type: "selection" },
		{
			label: "任务包名称",
			prop: "packageName",
			minWidth: 200,
		},
		{
			label: "所属部门",
			prop: "departmentName",
			minWidth: 120,
			render: (scope) => {
				return h(DepartmentTag, {
					departmentId: scope.departmentId,
					departmentName: scope.departmentName,
					size: "small",
					clickable: true,
					onClick: () => {
						// 点击部门标签时筛选该部门
						selectedDepartments.value = [scope.departmentId];
						onDepartmentFilterChange([scope.departmentId]);
					}
				});
			}
		},
		{
			label: "场景名称",
			prop: "scenarioName",
			minWidth: 150,
		},
		{
			label: "描述",
			prop: "description",
			minWidth: 200,
			showOverflowTooltip: true,
		},
		{
			label: "状态",
			prop: "packageStatus",
			minWidth: 100,
			dict: [
				{ label: "待分配", value: 0, color: "warning" },
				{ label: "执行中", value: 1, color: "primary" },
				{ label: "已完成", value: 2, color: "success" },
				{ label: "已关闭", value: 3, color: "info" },
			],
		},
		{
			label: "总任务数",
			prop: "totalTasks",
			minWidth: 100,
		},
		{
			label: "已完成",
			prop: "completedTasks",
			minWidth: 100,
		},
		{
			label: "完成率",
			prop: "completionRate",
			minWidth: 100,
			component: {
				name: "el-progress",
				props: (scope) => ({
					percentage: Number(scope.completionRate) || 0,
					format: () => `${scope.completionRate || 0}%`,
				}),
			},
		},
		{
			label: "计划开始时间",
			prop: "expectedStartTime",
			minWidth: 160,
			sortable: "custom",
			component: {
				name: "cl-date-text",
				props: { format: "YYYY-MM-DD HH:mm" }
			},
		},
		{
			label: "计划结束时间",
			prop: "expectedEndTime",
			minWidth: 160,
			sortable: "custom",
			component: {
				name: "cl-date-text",
				props: { format: "YYYY-MM-DD HH:mm" }
			},
		},
		{
			label: t("创建时间"),
			prop: "createTime",
			minWidth: 170,
			sortable: "desc",
			component: { name: "cl-date-text" },
		},
		{ type: "op", buttons: ["edit", "delete"] },
	],
});

// cl-search
const Search = useSearch({
	items: [
		{
			label: "任务包名称",
			prop: "packageName",
			component: { name: "el-input", props: { placeholder: "请输入任务包名称" } },
		},
		{
			label: "场景名称",
			prop: "scenarioName",
			component: { name: "el-input", props: { placeholder: "请输入场景名称" } },
		},
		{
			label: "状态",
			prop: "packageStatus",
			component: {
				name: "el-select",
				props: { placeholder: "请选择状态", clearable: true },
				options: [
					{ label: "待分配", value: 0 },
					{ label: "执行中", value: 1 },
					{ label: "已完成", value: 2 },
					{ label: "已关闭", value: 3 },
				],
			},
		},
	],
});

// cl-crud
const Crud = useCrud(
	{
		service: service.task.package,
	},
	(app) => {
		app.refresh();
	},
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}

// 处理行点击事件
const handleRowClick = async (row: any) => {
	console.log('点击了任务包行:', row);
	try {
		// 先只获取任务数据
		const tasksResponse = await service.task.info.page({
			packageId: row.id,
			size: 1000
		});

		console.log('后端返回的任务数据:', tasksResponse);

		selectedPackage.value = row;

		// 处理任务数据
		if (tasksResponse.list) {
			packageTasks.value = tasksResponse.list;
		} else if (tasksResponse.records) {
			packageTasks.value = tasksResponse.records;
		} else if (Array.isArray(tasksResponse)) {
			packageTasks.value = tasksResponse;
		} else {
			packageTasks.value = [];
		}

		// 从任务数据中提取步骤信息
		const stepMap = new Map();

		packageTasks.value.forEach((task, index) => {
			// 尝试多种可能的步骤字段
			const stepCode = task.stepCode || task.taskCategory || task.category || `STEP_${task.id}`;
			const stepName = task.stepName || task.taskCategoryName || task.categoryName || task.name || '未知步骤';

			if (stepCode) {
				stepMap.set(stepCode, {
					id: task.stepId || stepCode,
					stepCode: stepCode,
					stepName: stepName,
					status: 1, // 默认启用
					description: task.stepDescription || task.description || `${stepName}相关任务`
				});
			}
		});

		// 转换为数组并排序
		packageSteps.value = Array.from(stepMap.values()).sort((a, b) => {
			return a.stepCode.localeCompare(b.stepCode);
		});

		// 如果没有提取到步骤，创建一个默认步骤
		if (packageSteps.value.length === 0 && packageTasks.value.length > 0) {
			packageSteps.value = [{
				id: 'DEFAULT',
				stepCode: 'DEFAULT',
				stepName: '任务执行',
				status: 1,
				description: '所有任务的执行步骤'
			}];

			// 为所有任务分配默认步骤
			packageTasks.value = packageTasks.value.map(task => ({
				...task,
				stepCode: 'DEFAULT'
			}));
		}

		drawerVisible.value = true;
	} catch (error) {
		console.error('加载数据失败:', error);
		ElMessage.error('加载数据失败');
	}
};

// 任务更新回调
const handleTaskUpdated = () => {
	// 刷新表格数据
	refresh();
};

// 部门筛选处理
const onDepartmentFilterChange = (departmentIds: any[]) => {
	console.log('部门筛选变更:', departmentIds);
	
	// 更新选中的部门列表
	selectedDepartments.value = departmentIds;
	
	// 刷新表格
	(Table as any).value?.refresh();
};

// 刷新任务包下的任务和步骤（执行人调整后自动调用）
const handleTaskFlowRefresh = async () => {
	if (!selectedPackage.value) return;
	try {
		const tasksResponse = await service.task.info.page({
			packageId: selectedPackage.value.id,
			size: 1000
		});

		// 处理任务数据
		if (tasksResponse.list) {
			packageTasks.value = tasksResponse.list;
		} else if (tasksResponse.records) {
			packageTasks.value = tasksResponse.records;
		} else if (Array.isArray(tasksResponse)) {
			packageTasks.value = tasksResponse;
		} else {
			packageTasks.value = [];
		}

		// 从任务数据中提取步骤信息
		const stepMap = new Map();
		packageTasks.value.forEach((task) => {
			const stepCode = task.stepCode || task.taskCategory || task.category || `STEP_${task.id}`;
			const stepName = task.stepName || task.taskCategoryName || task.categoryName || task.name || '未知步骤';
			if (stepCode) {
				stepMap.set(stepCode, {
					id: task.stepId || stepCode,
					stepCode: stepCode,
					stepName: stepName,
					status: 1,
					description: task.stepDescription || task.description || `${stepName}相关任务`
				});
			}
		});
		packageSteps.value = Array.from(stepMap.values()).sort((a, b) => {
			return a.stepCode.localeCompare(b.stepCode);
		});
		if (packageSteps.value.length === 0 && packageTasks.value.length > 0) {
			packageSteps.value = [{
				id: 'DEFAULT',
				stepCode: 'DEFAULT',
				stepName: '任务执行',
				status: 1,
				description: '所有任务的执行步骤'
			}];
			packageTasks.value = packageTasks.value.map(task => ({
				...task,
				stepCode: 'DEFAULT'
			}));
		}
	} catch (error) {
		console.error('刷新任务包数据失败:', error);
		ElMessage.error('刷新任务包数据失败');
	}
};
</script>
