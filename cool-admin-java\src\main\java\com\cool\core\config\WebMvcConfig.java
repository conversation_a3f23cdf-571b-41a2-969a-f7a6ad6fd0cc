package com.cool.core.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置
 * 确保静态资源路径不与API路径冲突
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 禁用Spring Boot默认的静态资源映射
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(31556926);
                
        registry.addResourceHandler("/upload/**")
                .addResourceLocations("file:./assets/public/upload/")
                .setCachePeriod(31556926);
                
        registry.addResourceHandler("/favicon.ico")
                .addResourceLocations("classpath:/static/");
                
        // 确保/admin/**路径不会被静态资源处理器拦截
        // 这里不添加/admin/**的静态资源映射
    }
} 