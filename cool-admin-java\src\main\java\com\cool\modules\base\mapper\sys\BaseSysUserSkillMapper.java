package com.cool.modules.base.mapper.sys;

import com.cool.modules.base.entity.sys.BaseSysUserSkillEntity;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户技能Mapper
 */
@Mapper
public interface BaseSysUserSkillMapper extends BaseMapper<BaseSysUserSkillEntity> {

    /**
     * 根据用户ID查询技能列表
     */
    List<BaseSysUserSkillEntity> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据技能名称查询用户列表
     */
    List<Long> selectUserIdsBySkillName(@Param("skillName") String skillName);

    /**
     * 根据技能名称和等级查询用户列表
     */
    List<Long> selectUserIdsBySkillNameAndLevel(@Param("skillName") String skillName, @Param("minLevel") Integer minLevel);

    /**
     * 批量删除用户技能
     */
    int deleteByUserId(@Param("userId") Long userId);
}
