<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "project",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";

const { service } = useCool();
const { t } = useI18n();

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: t("项目编码"),
			prop: "code",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("项目编码1"),
			prop: "code1",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("项目名称"),
			prop: "name",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("项目别名"),
			prop: "alias",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("项目经理"),
			prop: "manager",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("自有员工数量"),
			prop: "staffCount",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("外包员工数量"),
			prop: "outsourceCount",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("所属区域名称"),
			prop: "belongDeptName",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("所属城区名称"),
			prop: "belongCityName",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("所在省"),
			prop: "province",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("所在市"),
			prop: "city",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("所在区"),
			prop: "district",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("街道/镇"),
			prop: "street",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("目标城市等级"),
			prop: "cityLevel",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("详细地址"),
			prop: "address",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("经度"),
			prop: "longitude",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("纬度"),
			prop: "latitude",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("职场级别"),
			prop: "jobLevel",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("主业态（一级）"),
			prop: "mainType1",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("主业态（二级）"),
			prop: "mainType2",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("业委会成立情况"),
			prop: "established",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("首次签约时间"),
			prop: "firstSignDate",
			component: {
				name: "el-date-picker",
				props: { type: "date", valueFormat: "YYYY-MM-DD" },
			},
			span: 12,
		},
		{
			label: t("实际进场时间"),
			prop: "actualEntryDate",
			component: {
				name: "el-date-picker",
				props: { type: "date", valueFormat: "YYYY-MM-DD" },
			},
			span: 12,
		},
		{
			label: t("关键能耗收费时间"),
			prop: "publicRevenueDate",
			component: {
				name: "el-date-picker",
				props: { type: "date", valueFormat: "YYYY-MM-DD" },
			},
			span: 12,
		},
		{
			label: t("是否合资公司项目"),
			prop: "isFundProject",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("项目生命周期"),
			prop: "projectLifeCycle",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("撤场日期"),
			prop: "exitDate",
			component: {
				name: "el-date-picker",
				props: { type: "date", valueFormat: "YYYY-MM-DD" },
			},
			span: 12,
		},
		{
			label: t("撤场原因"),
			prop: "exitReason",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("所属管理处名称"),
			prop: "mgmtOfficeName",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("所属管理处编码"),
			prop: "mgmtOfficeCode",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("运营状态"),
			prop: "operateStatus",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("产权属性"),
			prop: "propertyRight",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("收费方式"),
			prop: "chargeMode",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("收费标准（m²/月）"),
			prop: "chargeStandard",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("物业类型"),
			prop: "propertyType",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("是否共融项目"),
			prop: "isJointProject",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("总房产建筑面积"),
			prop: "totalBuildArea",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("收费面积"),
			prop: "chargeArea",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("户数"),
			prop: "householdCount",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("总建筑面积（m²）"),
			prop: "totalArea",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("占地面积（m²）"),
			prop: "landArea",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("地下室建筑面积（m²）"),
			prop: "undergroundArea",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("绿化面积（m²）"),
			prop: "greenArea",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("容积率"),
			prop: "plotRatio",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("是否有停车场"),
			prop: "hasParking",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("总车位个数"),
			prop: "totalParkingCount",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("停车场是否自管"),
			prop: "selfParkingMgmt",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("室内停车场收费时间"),
			prop: "indoorParkingDate",
			component: {
				name: "el-date-picker",
				props: { type: "date", valueFormat: "YYYY-MM-DD" },
			},
			span: 12,
		},
		{
			label: t("出入口数量（个）"),
			prop: "entranceCount",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("门控室数量（个）"),
			prop: "accessCount",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("消防控制室数量（个）"),
			prop: "fireControlCount",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("升降系统数量（个）"),
			prop: "elevatorCount",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("中央空调主机数量（个）"),
			prop: "centralAcCount",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("供暖锅炉数量（个）"),
			prop: "boilerCount",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("审批状态"),
			prop: "approvalStatus",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("时间范围"),
			prop: "time",
			component: {
				name: "el-date-picker",
				props: {
					type: "datetimerange",
					valueFormat: "YYYY-MM-DD HH:mm:ss",
					defaultTime: [
						"2000-01-31T16:00:00.000Z",
						"2000-02-01T15:59:59.000Z",
					],
				},
			},
			span: 12,
			hook: "datetimeRange",
		},
	],
});

// cl-table
const Table = useTable({
	columns: [
		{ type: "selection" },
		{ label: t("项目编码"), prop: "code", minWidth: 120 },
		// { label: t("项目编码1"), prop: "code1", minWidth: 120 },
		{ label: t("项目名称"), prop: "name", minWidth: 120 },
		// { label: t("项目别名"), prop: "alias", minWidth: 120 },
		// { label: t("项目经理"), prop: "manager", minWidth: 120 },
		// { label: t("自有员工数量"), prop: "staffCount", minWidth: 120 },
		// { label: t("外包员工数量"), prop: "outsourceCount", minWidth: 120 },
		// { label: t("所属区域名称"), prop: "belongDeptName", minWidth: 120 },
		// { label: t("所属城区名称"), prop: "belongCityName", minWidth: 120 },
		// { label: t("所在省"), prop: "province", minWidth: 120 },
		// { label: t("所在市"), prop: "city", minWidth: 120 },
		// { label: t("所在区"), prop: "district", minWidth: 120 },
		// { label: t("街道/镇"), prop: "street", minWidth: 120 },
		// { label: t("目标城市等级"), prop: "cityLevel", minWidth: 120 },
		// { label: t("详细地址"), prop: "address", minWidth: 120 },
		// { label: t("经度"), prop: "longitude", minWidth: 120 },
		// { label: t("纬度"), prop: "latitude", minWidth: 120 },
		// { label: t("职场级别"), prop: "jobLevel", minWidth: 120 },
		// { label: t("主业态（一级）"), prop: "mainType1", minWidth: 120 },
		// { label: t("主业态（二级）"), prop: "mainType2", minWidth: 120 },
		// { label: t("业委会成立情况"), prop: "established", minWidth: 120 },
		// {
		// 	label: t("首次签约时间"),
		// 	prop: "firstSignDate",
		// 	minWidth: 140,
		// 	sortable: "custom",
		// 	component: {
		// 		name: "cl-date-text",
		// 		props: { format: "YYYY-MM-DD" },
		// 	},
		// },
		// {
		// 	label: t("实际进场时间"),
		// 	prop: "actualEntryDate",
		// 	minWidth: 140,
		// 	sortable: "custom",
		// 	component: {
		// 		name: "cl-date-text",
		// 		props: { format: "YYYY-MM-DD" },
		// 	},
		// },
		// {
		// 	label: t("关键能耗收费时间"),
		// 	prop: "publicRevenueDate",
		// 	minWidth: 140,
		// 	sortable: "custom",
		// 	component: {
		// 		name: "cl-date-text",
		// 		props: { format: "YYYY-MM-DD" },
		// 	},
		// },
		// { label: t("是否合资公司项目"), prop: "isFundProject", minWidth: 120 },
		// { label: t("项目生命周期"), prop: "projectLifeCycle", minWidth: 120 },
		// {
		// 	label: t("撤场日期"),
		// 	prop: "exitDate",
		// 	minWidth: 140,
		// 	sortable: "custom",
		// 	component: {
		// 		name: "cl-date-text",
		// 		props: { format: "YYYY-MM-DD" },
		// 	},
		// },
		// { label: t("撤场原因"), prop: "exitReason", minWidth: 120 },
		// { label: t("所属管理处名称"), prop: "mgmtOfficeName", minWidth: 120 },
		// { label: t("所属管理处编码"), prop: "mgmtOfficeCode", minWidth: 120 },
		// { label: t("运营状态"), prop: "operateStatus", minWidth: 120 },
		{ label: t("产权属性"), prop: "propertyRight", minWidth: 120 },
		// { label: t("收费方式"), prop: "chargeMode", minWidth: 120 },
		{
			label: t("收费标准（m²/月）"),
			prop: "chargeStandard",
			minWidth: 120,
		},
		{ label: t("物业类型"), prop: "propertyType", minWidth: 120 },
		// { label: t("是否共融项目"), prop: "isJointProject", minWidth: 120 },
		// { label: t("总房产建筑面积"), prop: "totalBuildArea", minWidth: 120 },
		{ label: t("收费面积"), prop: "chargeArea", minWidth: 120 },
		// { label: t("户数"), prop: "householdCount", minWidth: 120 },
		// { label: t("总建筑面积（m²）"), prop: "totalArea", minWidth: 120 },
		// { label: t("占地面积（m²）"), prop: "landArea", minWidth: 120 },
		// {
		// 	label: t("地下室建筑面积（m²）"),
		// 	prop: "undergroundArea",
		// 	minWidth: 120,
		// },
		// { label: t("绿化面积（m²）"), prop: "greenArea", minWidth: 120 },
		// { label: t("容积率"), prop: "plotRatio", minWidth: 120 },
		// { label: t("是否有停车场"), prop: "hasParking", minWidth: 120 },
		// { label: t("总车位个数"), prop: "totalParkingCount", minWidth: 120 },
		// { label: t("停车场是否自管"), prop: "selfParkingMgmt", minWidth: 120 },
		// {
		// 	label: t("室内停车场收费时间"),
		// 	prop: "indoorParkingDate",
		// 	minWidth: 140,
		// 	sortable: "custom",
		// 	component: {
		// 		name: "cl-date-text",
		// 		props: { format: "YYYY-MM-DD" },
		// 	},
		// },
		// { label: t("出入口数量（个）"), prop: "entranceCount", minWidth: 120 },
		// { label: t("门控室数量（个）"), prop: "accessCount", minWidth: 120 },
		// {
		// 	label: t("消防控制室数量（个）"),
		// 	prop: "fireControlCount",
		// 	minWidth: 120,
		// },
		// {
		// 	label: t("升降系统数量（个）"),
		// 	prop: "elevatorCount",
		// 	minWidth: 120,
		// },
		// {
		// 	label: t("中央空调主机数量（个）"),
		// 	prop: "centralAcCount",
		// 	minWidth: 120,
		// },
		// { label: t("供暖锅炉数量（个）"), prop: "boilerCount", minWidth: 120 },
		// { label: t("审批状态"), prop: "approvalStatus", minWidth: 120 },
		// {
		// 	label: t("开始时间"),
		// 	prop: "startTime",
		// 	minWidth: 170,
		// 	sortable: "custom",
		// 	component: { name: "cl-date-text" },
		// },
		{
			label: t("结束时间"),
			prop: "endTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ type: "op", buttons: ["edit", "delete"] },
	],
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
	{
		service: service.project,
	},
	(app) => {
		app.refresh();
	},
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
