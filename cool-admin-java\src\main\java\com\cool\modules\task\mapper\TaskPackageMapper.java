package com.cool.modules.task.mapper;


import com.cool.modules.task.entity.TaskPackageEntity;
import com.mybatisflex.core.BaseMapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 场景任务包Mapper
 */
@Mapper
public interface TaskPackageMapper extends BaseMapper<TaskPackageEntity> {

    /**
     * 更新任务包统计信息
     */
    void updatePackageStats(@Param("packageId") Long packageId,
                           @Param("totalTasks") Integer totalTasks,
                           @Param("completedTasks") Integer completedTasks,
                           @Param("inProgressTasks") Integer inProgressTasks,
                           @Param("pendingTasks") Integer pendingTasks,
                           @Param("completionRate") Integer completionRate);

    /**
     * 获取任务包统计信息
     */
    List<Map<String, Object>> getPackageStatsWithTasks();

    /**
     * 根据场景ID获取任务包列表
     */
    List<TaskPackageEntity> getPackagesByScenarioId(@Param("scenarioId") Long scenarioId);

    /**
     * 获取任务包详情（包含统计信息）
     */
    Map<String, Object> getPackageDetailWithStats(@Param("packageId") Long packageId);

    /**
     * 分页查询任务包列表（关联部门信息）
     */
    List<TaskPackageEntity> selectPackagesWithDepartment(@Param("params") Map<String, Object> params);

    /**
     * 查询任务包列表总数（关联部门信息）
     */
    int countPackagesWithDepartment(@Param("params") Map<String, Object> params);

    /**
     * 根据ID获取任务包详情（关联部门信息）
     */
    TaskPackageEntity selectPackageWithDepartmentById(@Param("packageId") Long packageId);
}
