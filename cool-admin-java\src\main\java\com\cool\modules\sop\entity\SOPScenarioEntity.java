package com.cool.modules.sop.entity;

import org.dromara.autotable.annotation.Index;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;

import lombok.Getter;
import lombok.Setter;

/**
 * 场景SOP主表实体
 * 唯一约束：行业ID + 模块编码 + 场景编码 + 版本
 */
@Getter
@Setter
@Table(value = "sop_scenario", comment = "场景SOP主表")
public class SOPScenarioEntity extends BaseEntity<SOPScenarioEntity> {

    @Index
    @ColumnDefine(comment = "所属行业ID", type = "bigint")
    private Long industryId;

    @ColumnDefine(comment = "所属行业名称", length = 100)
    private String industryName;

    @ColumnDefine(comment = "阶段（支持多级数据字典）", length = 50)
    private String stage;

    @Index
    @ColumnDefine(comment = "模块编码", length = 20, notNull = true)
    private String moduleCode;

    @ColumnDefine(comment = "模块名称", length = 100, notNull = true)
    private String moduleName;

    @Index
    @ColumnDefine(comment = "场景编码", length = 20, notNull = true)
    private String scenarioCode;

    @ColumnDefine(comment = "场景名称", length = 200, notNull = true)
    private String scenarioName;

    @ColumnDefine(comment = "执行周期（自然语言描述，后续AI转换）", length = 100)
    private String executionCycle;

    @ColumnDefine(comment = "AI转换后的执行频率（日/周/月/季度/半年/年）", length = 50)
    private String executionFrequency;

    @ColumnDefine(comment = "执行次数")
    private Integer executionCount;

    @Index
    @ColumnDefine(comment = "版本号", length = 20, defaultValue = "1.0")
    private String version;
    
    @ColumnDefine(comment = "状态(0:草稿 1:启用 2:禁用 3:已归档)", defaultValue = "1")
    private Integer status;

    @ColumnDefine(comment = "场景描述", type = "text")
    private String description;

    @ColumnDefine(comment = "总步骤数", defaultValue = "0")
    private Integer totalSteps;

    @ColumnDefine(comment = "预计总耗时(分钟)")
    private Integer estimatedDuration;

    @ColumnDefine(comment = "难度等级(1-5)", defaultValue = "3")
    private Integer difficultyLevel;

    @ColumnDefine(comment = "质量标准", type = "text")
    private String qualityStandard;

    @ColumnDefine(comment = "成功标准", type = "text")
    private String successCriteria;

    @ColumnDefine(comment = "风险点说明", type = "text")
    private String riskPoints;

    @ColumnDefine(comment = "注意事项", type = "text")
    private String attentionPoints;

    @ColumnDefine(comment = "适用区域", length = 200)
    private String applicableArea;
} 