package com.cool.modules.organization.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.cool.core.exception.CoolException;
import com.cool.modules.base.entity.sys.BaseSysUserEntity;
import com.cool.modules.base.service.sys.BaseSysUserService;
import com.cool.modules.organization.dto.ProjectMemberDTO;
import com.cool.modules.organization.entity.ProjectInfoEntity;
import com.cool.modules.organization.entity.UserOrganizationEntity;
import com.cool.modules.organization.enums.GlobalProjectRoleEnum;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import com.cool.modules.organization.mapper.UserOrganizationMapper;
import com.cool.modules.organization.service.ProjectInfoService;
import com.cool.modules.organization.service.UserOrganizationService;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户组织关系服务实现
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserOrganizationServiceImpl extends ServiceImpl<UserOrganizationMapper, UserOrganizationEntity> 
        implements UserOrganizationService {
    
    private final BaseSysUserService baseSysUserService;
    private final ProjectInfoService projectInfoService;
    
    @Override
    public List<UserOrganizationEntity> getByUserIdAndType(Long userId, String organizationType) {
        if (userId == null || organizationType == null) {
            return new ArrayList<>();
        }
        
        return mapper.getByUserIdAndType(userId, organizationType);
    }
    
    @Override
    public List<UserOrganizationEntity> getByOrganizationIdAndType(Long organizationId, String organizationType) {
        if (organizationId == null || organizationType == null) {
            return new ArrayList<>();
        }
        
        return mapper.getByOrganizationIdAndType(organizationId, organizationType);
    }
    
    @Override
    public boolean existsByUserAndOrganization(Long userId, Long organizationId, String organizationType) {
        if (userId == null || organizationId == null || organizationType == null) {
            return false;
        }
        
        return mapper.existsByUserAndOrganization(userId, organizationId, organizationType);
    }
    
    @Override
    public String getUserRoleInOrganization(Long userId, Long organizationId, String organizationType) {
        if (userId == null || organizationId == null || organizationType == null) {
            return null;
        }
        
        return mapper.getUserRoleInOrganization(userId, organizationId, organizationType);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addUserToOrganization(Long userId, Long organizationId, String organizationType, 
                                        String roleCode, Long assignerId) {
        // 参数验证
        if (userId == null || organizationId == null || organizationType == null || roleCode == null) {
            throw new CoolException("参数不能为空");
        }
        
        // 检查用户是否已在组织中
        if (existsByUserAndOrganization(userId, organizationId, organizationType)) {
            throw new CoolException("用户已在该组织中");
        }
        
        // 验证角色代码
        if (OrganizationModeEnum.PROJECT.getCode().equals(organizationType)) {
            if (!GlobalProjectRoleEnum.isValidCode(roleCode)) {
                throw new CoolException("无效的项目角色代码: " + roleCode);
            }
        }
        
        try {
            UserOrganizationEntity entity = new UserOrganizationEntity();
            entity.setUserId(userId);
            entity.setOrganizationId(organizationId);
            entity.setOrganizationType(organizationType);
            entity.setRoleCode(roleCode);
            entity.setStatus(1);
            entity.setJoinTime(new Date());
            entity.setAssignerId(assignerId);
            entity.setAssignTime(new Date());
            entity.setIsPrimary(false);
            entity.setOrderNum(0);
            
            boolean result = save(entity);
            
            if (result) {
                log.info("用户 {} 成功加入组织 {} (类型: {}, 角色: {})", userId, organizationId, organizationType, roleCode);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("添加用户到组织失败", e);
            throw new CoolException("添加用户到组织失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeUserFromOrganization(Long userId, Long organizationId, String organizationType) {
        if (userId == null || organizationId == null || organizationType == null) {
            return false;
        }
        
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("user_id", userId)
                .eq("organization_id", organizationId)
                .eq("organization_type", organizationType);
            
            boolean result = remove(queryWrapper);
            
            if (result) {
                log.info("用户 {} 成功从组织 {} (类型: {}) 中移除", userId, organizationId, organizationType);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("从组织中移除用户失败", e);
            throw new CoolException("从组织中移除用户失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserRoleInOrganization(Long userId, Long organizationId, String organizationType, String roleCode) {
        if (userId == null || organizationId == null || organizationType == null || roleCode == null) {
            return false;
        }
        
        // 验证角色代码
        if (OrganizationModeEnum.PROJECT.getCode().equals(organizationType)) {
            if (!GlobalProjectRoleEnum.isValidCode(roleCode)) {
                throw new CoolException("无效的项目角色代码: " + roleCode);
            }
        }
        
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("user_id", userId)
                .eq("organization_id", organizationId)
                .eq("organization_type", organizationType);
            
            UserOrganizationEntity entity = getOne(queryWrapper);
            if (entity == null) {
                throw new CoolException("用户不在该组织中");
            }
            
            entity.setRoleCode(roleCode);
            entity.setUpdateTime(new Date());
            
            boolean result = updateById(entity);
            
            if (result) {
                log.info("用户 {} 在组织 {} (类型: {}) 中的角色已更新为 {}", userId, organizationId, organizationType, roleCode);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("更新用户组织角色失败", e);
            throw new CoolException("更新用户组织角色失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchAddUsersToOrganization(List<Long> userIds, Long organizationId, String organizationType, 
                                          String roleCode, Long assignerId) {
        if (CollUtil.isEmpty(userIds) || organizationId == null || organizationType == null || roleCode == null) {
            return 0;
        }
        
        // 验证角色代码
        if (OrganizationModeEnum.PROJECT.getCode().equals(organizationType)) {
            if (!GlobalProjectRoleEnum.isValidCode(roleCode)) {
                throw new CoolException("无效的项目角色代码: " + roleCode);
            }
        }
        
        int successCount = 0;
        Date now = new Date();
        
        for (Long userId : userIds) {
            try {
                // 检查用户是否已在组织中
                if (!existsByUserAndOrganization(userId, organizationId, organizationType)) {
                    UserOrganizationEntity entity = new UserOrganizationEntity();
                    entity.setUserId(userId);
                    entity.setOrganizationId(organizationId);
                    entity.setOrganizationType(organizationType);
                    entity.setRoleCode(roleCode);
                    entity.setStatus(1);
                    entity.setJoinTime(now);
                    entity.setAssignerId(assignerId);
                    entity.setAssignTime(now);
                    entity.setIsPrimary(false);
                    entity.setOrderNum(0);
                    
                    if (save(entity)) {
                        successCount++;
                    }
                }
            } catch (Exception e) {
                log.warn("批量添加用户 {} 到组织失败: {}", userId, e.getMessage());
            }
        }
        
        log.info("批量添加用户到组织 {} (类型: {})，成功 {} 个，总共 {} 个", 
                organizationId, organizationType, successCount, userIds.size());
        
        return successCount;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchRemoveUsersFromOrganization(List<Long> userIds, Long organizationId, String organizationType) {
        if (CollUtil.isEmpty(userIds) || organizationId == null || organizationType == null) {
            return 0;
        }
        
        try {
            int result = mapper.batchDeleteByUserAndOrganizations(null, Arrays.asList(organizationId), organizationType);
            
            log.info("批量从组织 {} (类型: {}) 中移除用户，成功 {} 个", organizationId, organizationType, result);
            
            return result;
            
        } catch (Exception e) {
            log.error("批量从组织中移除用户失败", e);
            throw new CoolException("批量从组织中移除用户失败: " + e.getMessage());
        }
    }
    
    @Override
    public List<Long> getUserProjectIds(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        
        List<UserOrganizationEntity> projectRoles = getByUserIdAndType(userId, OrganizationModeEnum.PROJECT.getCode());
        
        return projectRoles.stream()
            .filter(role -> role.getStatus() == 1)
            .filter(role -> role.getExpireTime() == null || role.getExpireTime().after(new Date()))
            .map(UserOrganizationEntity::getOrganizationId)
            .distinct()
            .collect(Collectors.toList());
    }
    
    @Override
    public String getHighestProjectRole(Long userId) {
        if (userId == null) {
            return null;
        }
        
        return mapper.getHighestProjectRole(userId);
    }
    
    @Override
    public List<ProjectMemberDTO> getProjectMembers(Long projectId) {
        if (projectId == null) {
            return new ArrayList<>();
        }
        
        List<UserOrganizationEntity> members = getByOrganizationIdAndType(projectId, OrganizationModeEnum.PROJECT.getCode());
        
        return members.stream()
            .map(this::convertToProjectMemberDTO)
            .collect(Collectors.toList());
    }
    
    @Override
    public boolean hasProjectManagePermission(Long userId, Long projectId) {
        if (userId == null || projectId == null) {
            return false;
        }
        
        String roleCode = getUserRoleInOrganization(userId, projectId, OrganizationModeEnum.PROJECT.getCode());
        
        return GlobalProjectRoleEnum.hasManagePermission(roleCode);
    }
    
    private ProjectMemberDTO convertToProjectMemberDTO(UserOrganizationEntity entity) {
        ProjectMemberDTO dto = new ProjectMemberDTO();
        BeanUtil.copyProperties(entity, dto);
        
        // 填充用户信息
        BaseSysUserEntity user = baseSysUserService.getById(entity.getUserId());
        if (user != null) {
            dto.setUserName(user.getName());
            dto.setUserEmail(user.getEmail());
            dto.setUserPhone(user.getPhone());
            dto.setUserAvatar(user.getHeadImg());
        }
        
        // 填充项目信息
        ProjectInfoEntity project = projectInfoService.getById(entity.getOrganizationId());
        if (project != null) {
            dto.setProjectName(project.getProjectName());
        }
        
        // 填充角色名称
        GlobalProjectRoleEnum role = GlobalProjectRoleEnum.getByCode(entity.getRoleCode());
        if (role != null) {
            dto.setRoleName(role.getName());
        }
        
        return dto;
    }
}
