package com.cool.modules.task.entity;

import com.cool.core.base.BaseEntity;
import com.cool.modules.task.enums.TaskExecutionStatusEnum;
import com.cool.modules.task.enums.AssignmentTypeEnum;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.annotation.Column;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Table(value = "task_execution", comment = "任务执行人信息")
public class TaskExecutionEntity extends BaseEntity<TaskExecutionEntity> {

    @ColumnDefine(comment = "任务ID", notNull = true)
    private Long taskId;

    @ColumnDefine(comment = "执行人ID", notNull = true)
    private Long assigneeId;
    
    @ColumnDefine(comment = "执行人姓名", length = 100)
    private String assigneeName;

    @ColumnDefine(comment = "执行状态", notNull = true, length = 50) // ASSIGNED, ACCEPTED, IN_PROGRESS, COMPLETED, REJECTED, CANCELLED
    private String executionStatus;

    @ColumnDefine(comment = "接单时间")
    private Date acceptTime;

    @ColumnDefine(comment = "完成时间")
    private Date completionTime;

    @ColumnDefine(comment = "完成说明", type = "text")
    private String completionNote;

    @ColumnDefine(comment = "附件列表(JSON)", type = "text")
    private String attachments;

    @ColumnDefine(comment = "照片", type = "text") // Store as JSON array of strings
    private String photos;
    
    @ColumnDefine(comment = "执行备注", type = "text")
    private String remark;

    // LLM调度相关字段
    @ColumnDefine(comment = "分配方式", length = 20, defaultValue = "MANUAL") // MANUAL, AUTO, AI, BATCH
    private String assignmentType;

    @ColumnDefine(comment = "AI推荐置信度 0-100", defaultValue = "0")
    private Integer confidence;

    @ColumnDefine(comment = "分配理由", type = "text")
    private String assignmentReasons;

    @ColumnDefine(comment = "分配人ID", type = "bigint")
    private Long assignedBy;

    /**
     * 执行部门ID
     */
    @ColumnDefine(comment = "执行部门ID", type = "bigint")
    private Long departmentId;

    /**
     * 执行部门名称 (查询时填充)
     */
    @Column(ignore = true)
    private String departmentName;

    /**
     * 执行人部门ID
     */
    @ColumnDefine(comment = "执行人部门ID", type = "bigint")
    private Long assigneeDepartmentId;

    /**
     * 执行人部门名称 (查询时填充)
     */
    @Column(ignore = true)
    private String assigneeDepartmentName;

    // ========== 枚举相关便捷方法 ==========

    /**
     * 设置执行状态（使用枚举）
     */
    public void setExecutionStatusEnum(TaskExecutionStatusEnum status) {
        this.executionStatus = status != null ? status.getCode() : null;
    }

    /**
     * 获取执行状态枚举
     */
    public TaskExecutionStatusEnum getExecutionStatusEnum() {
        return TaskExecutionStatusEnum.getByCode(this.executionStatus);
    }

    /**
     * 获取执行状态名称
     */
    public String getExecutionStatusName() {
        return TaskExecutionStatusEnum.getNameByCode(this.executionStatus);
    }

    /**
     * 设置分配类型（使用枚举）
     */
    public void setAssignmentTypeEnum(AssignmentTypeEnum type) {
        this.assignmentType = type != null ? type.getCode() : null;
    }

    /**
     * 获取分配类型枚举
     */
    public AssignmentTypeEnum getAssignmentTypeEnum() {
        return AssignmentTypeEnum.getByCode(this.assignmentType);
    }

    /**
     * 获取分配类型名称
     */
    public String getAssignmentTypeName() {
        return AssignmentTypeEnum.getNameByCode(this.assignmentType);
    }

    /**
     * 检查是否为进行中状态
     */
    public boolean isActiveStatus() {
        return TaskExecutionStatusEnum.isActiveStatus(this.executionStatus);
    }

    /**
     * 检查是否为完成状态
     */
    public boolean isCompletedStatus() {
        return TaskExecutionStatusEnum.isCompletedStatus(this.executionStatus);
    }

    /**
     * 检查是否为终止状态
     */
    public boolean isTerminalStatus() {
        return TaskExecutionStatusEnum.isTerminalStatus(this.executionStatus);
    }

    /**
     * 检查是否为自动分配
     */
    public boolean isAutoAssignment() {
        return AssignmentTypeEnum.isAutoType(this.assignmentType);
    }

    /**
     * 检查是否为手动分配
     */
    public boolean isManualAssignment() {
        return AssignmentTypeEnum.isManualType(this.assignmentType);
    }
}