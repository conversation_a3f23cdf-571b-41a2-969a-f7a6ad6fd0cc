package com.cool.modules.project.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class ProjectEntityTableDef extends TableDef {

    /**
     * 项目实体
     */
    public static final ProjectEntityTableDef PROJECT_ENTITY = new ProjectEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn CITY = new QueryColumn(this, "city");

    public final QueryColumn CODE = new QueryColumn(this, "code");

    public final QueryColumn NAME = new QueryColumn(this, "name");

    public final QueryColumn ALIAS = new QueryColumn(this, "alias");

    public final QueryColumn CODE1 = new QueryColumn(this, "code1");

    public final QueryColumn STREET = new QueryColumn(this, "street");

    public final QueryColumn ADDRESS = new QueryColumn(this, "address");

    public final QueryColumn END_TIME = new QueryColumn(this, "end_time");

    public final QueryColumn MANAGER = new QueryColumn(this, "manager");

    public final QueryColumn DISTRICT = new QueryColumn(this, "district");

    public final QueryColumn EXIT_DATE = new QueryColumn(this, "exit_date");

    public final QueryColumn JOB_LEVEL = new QueryColumn(this, "job_level");

    public final QueryColumn LAND_AREA = new QueryColumn(this, "land_area");

    public final QueryColumn LATITUDE = new QueryColumn(this, "latitude");

    public final QueryColumn PROVINCE = new QueryColumn(this, "province");

    public final QueryColumn CITY_LEVEL = new QueryColumn(this, "city_level");

    public final QueryColumn GREEN_AREA = new QueryColumn(this, "green_area");

    public final QueryColumn LONGITUDE = new QueryColumn(this, "longitude");

    public final QueryColumn MAIN_TYPE1 = new QueryColumn(this, "main_type1");

    public final QueryColumn MAIN_TYPE2 = new QueryColumn(this, "main_type2");

    public final QueryColumn PLOT_RATIO = new QueryColumn(this, "plot_ratio");

    public final QueryColumn START_TIME = new QueryColumn(this, "start_time");

    public final QueryColumn TOTAL_AREA = new QueryColumn(this, "total_area");

    public final QueryColumn CHARGE_AREA = new QueryColumn(this, "charge_area");

    public final QueryColumn CHARGE_MODE = new QueryColumn(this, "charge_mode");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn EXIT_REASON = new QueryColumn(this, "exit_reason");

    public final QueryColumn HAS_PARKING = new QueryColumn(this, "has_parking");

    public final QueryColumn STAFF_COUNT = new QueryColumn(this, "staff_count");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    public final QueryColumn ACCESS_COUNT = new QueryColumn(this, "access_count");

    public final QueryColumn BOILER_COUNT = new QueryColumn(this, "boiler_count");

    public final QueryColumn ESTABLISHED = new QueryColumn(this, "established");

    public final QueryColumn PROPERTY_TYPE = new QueryColumn(this, "property_type");

    public final QueryColumn ELEVATOR_COUNT = new QueryColumn(this, "elevator_count");

    public final QueryColumn ENTRANCE_COUNT = new QueryColumn(this, "entrance_count");

    public final QueryColumn FIRST_SIGN_DATE = new QueryColumn(this, "first_sign_date");

    public final QueryColumn IS_FUND_PROJECT = new QueryColumn(this, "is_fund_project");

    public final QueryColumn OPERATE_STATUS = new QueryColumn(this, "operate_status");

    public final QueryColumn PROPERTY_RIGHT = new QueryColumn(this, "property_right");

    public final QueryColumn APPROVAL_STATUS = new QueryColumn(this, "approval_status");

    public final QueryColumn BELONG_CITY_NAME = new QueryColumn(this, "belong_city_name");

    public final QueryColumn BELONG_DEPT_NAME = new QueryColumn(this, "belong_dept_name");

    public final QueryColumn CENTRAL_AC_COUNT = new QueryColumn(this, "central_ac_count");

    public final QueryColumn CHARGE_STANDARD = new QueryColumn(this, "charge_standard");

    public final QueryColumn HOUSEHOLD_COUNT = new QueryColumn(this, "household_count");

    public final QueryColumn IS_JOINT_PROJECT = new QueryColumn(this, "is_joint_project");

    public final QueryColumn MGMT_OFFICE_CODE = new QueryColumn(this, "mgmt_office_code");

    public final QueryColumn MGMT_OFFICE_NAME = new QueryColumn(this, "mgmt_office_name");

    public final QueryColumn OUTSOURCE_COUNT = new QueryColumn(this, "outsource_count");

    public final QueryColumn TOTAL_BUILD_AREA = new QueryColumn(this, "total_build_area");

    public final QueryColumn ACTUAL_ENTRY_DATE = new QueryColumn(this, "actual_entry_date");

    public final QueryColumn SELF_PARKING_MGMT = new QueryColumn(this, "self_parking_mgmt");

    public final QueryColumn UNDERGROUND_AREA = new QueryColumn(this, "underground_area");

    public final QueryColumn FIRE_CONTROL_COUNT = new QueryColumn(this, "fire_control_count");

    public final QueryColumn PROJECT_LIFE_CYCLE = new QueryColumn(this, "project_life_cycle");

    public final QueryColumn INDOOR_PARKING_DATE = new QueryColumn(this, "indoor_parking_date");

    public final QueryColumn PUBLIC_REVENUE_DATE = new QueryColumn(this, "public_revenue_date");

    public final QueryColumn TOTAL_PARKING_COUNT = new QueryColumn(this, "total_parking_count");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, CITY, CODE, NAME, ALIAS, CODE1, STREET, ADDRESS, END_TIME, MANAGER, DISTRICT, EXIT_DATE, JOB_LEVEL, LAND_AREA, LATITUDE, PROVINCE, CITY_LEVEL, GREEN_AREA, LONGITUDE, MAIN_TYPE1, MAIN_TYPE2, PLOT_RATIO, START_TIME, TOTAL_AREA, CHARGE_AREA, CHARGE_MODE, CREATE_TIME, EXIT_REASON, HAS_PARKING, STAFF_COUNT, UPDATE_TIME, ACCESS_COUNT, BOILER_COUNT, ESTABLISHED, PROPERTY_TYPE, ELEVATOR_COUNT, ENTRANCE_COUNT, FIRST_SIGN_DATE, IS_FUND_PROJECT, OPERATE_STATUS, PROPERTY_RIGHT, APPROVAL_STATUS, BELONG_CITY_NAME, BELONG_DEPT_NAME, CENTRAL_AC_COUNT, CHARGE_STANDARD, HOUSEHOLD_COUNT, IS_JOINT_PROJECT, MGMT_OFFICE_CODE, MGMT_OFFICE_NAME, OUTSOURCE_COUNT, TOTAL_BUILD_AREA, ACTUAL_ENTRY_DATE, SELF_PARKING_MGMT, UNDERGROUND_AREA, FIRE_CONTROL_COUNT, PROJECT_LIFE_CYCLE, INDOOR_PARKING_DATE, PUBLIC_REVENUE_DATE, TOTAL_PARKING_COUNT};

    public ProjectEntityTableDef() {
        super("", "project");
    }

    private ProjectEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public ProjectEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new ProjectEntityTableDef("", "project", alias));
    }

}
