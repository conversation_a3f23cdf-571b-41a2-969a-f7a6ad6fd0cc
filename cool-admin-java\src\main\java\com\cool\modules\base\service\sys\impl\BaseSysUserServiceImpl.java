package com.cool.modules.base.service.sys.impl;

import static com.cool.modules.base.entity.sys.table.BaseSysDepartmentEntityTableDef.BASE_SYS_DEPARTMENT_ENTITY;
import static com.cool.modules.base.entity.sys.table.BaseSysRoleEntityTableDef.BASE_SYS_ROLE_ENTITY;
import static com.cool.modules.base.entity.sys.table.BaseSysUserEntityTableDef.BASE_SYS_USER_ENTITY;
import static com.cool.modules.base.entity.sys.table.BaseSysUserRoleEntityTableDef.BASE_SYS_USER_ROLE_ENTITY;
import static com.mybatisflex.core.query.QueryMethods.groupConcat;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.json.JSONObject;
import com.cool.core.base.BaseServiceImpl;
import com.cool.core.base.ModifyEnum;
import com.cool.core.cache.CoolCache;
import com.cool.core.exception.CoolPreconditions;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.core.util.DatabaseDialectUtils;
import com.cool.modules.base.dto.UserQueryRequest;
import com.cool.modules.base.entity.sys.BaseSysDepartmentEntity;
import com.cool.modules.base.entity.sys.BaseSysUserEntity;
import com.cool.modules.base.mapper.sys.BaseSysDepartmentMapper;
import com.cool.modules.base.mapper.sys.BaseSysUserMapper;
import com.cool.modules.base.service.sys.BaseSysPermsService;
import com.cool.modules.base.service.sys.BaseSysUserService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateChain;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 系统用户
 */
@Service
@RequiredArgsConstructor
public class BaseSysUserServiceImpl extends BaseServiceImpl<BaseSysUserMapper, BaseSysUserEntity>
    implements BaseSysUserService {

    final private CoolCache coolCache;

    final private BaseSysPermsService baseSysPermsService;

    final private BaseSysDepartmentMapper baseSysDepartmentMapper;

    @Override
    public Object page(JSONObject requestParams, Page<BaseSysUserEntity> page, QueryWrapper qw) {
        String keyWord = requestParams.getStr("keyWord");
        Integer status = requestParams.getInt("status");
        Long[] departmentIds = requestParams.get("departmentIds", Long[].class);
        Long[] roleIds = requestParams.get("roleIds", Long[].class);
        JSONObject tokenInfo = CoolSecurityUtil.getAdminUserInfo(requestParams);
        // 用户的部门权限
        Long[] permsDepartmentArr = coolCache.get("admin:department:" + tokenInfo.get("userId"),
            Long[].class);
        if (DatabaseDialectUtils.isPostgresql()) {
            // 兼容postgresql
            qw.select("base_sys_user.id","base_sys_user.create_time","base_sys_user.department_id",
                "base_sys_user.email","base_sys_user.head_img","base_sys_user.name","base_sys_user.nick_name",
                "base_sys_user.phone","base_sys_user.remark","base_sys_user.status",
                "base_sys_user.update_time","base_sys_user.username",
                "string_agg(base_sys_role.name, ', ') AS roleName",
                "base_sys_department.name AS departmentName"
            );
        } else {
            qw.select(BASE_SYS_USER_ENTITY.ALL_COLUMNS,
                groupConcat(BASE_SYS_ROLE_ENTITY.NAME).as("roleName"),
                BASE_SYS_DEPARTMENT_ENTITY.NAME.as("departmentName")
            );
        }

        qw.from(BASE_SYS_USER_ENTITY).leftJoin(BASE_SYS_USER_ROLE_ENTITY)
            .on(BASE_SYS_USER_ENTITY.ID.eq(BASE_SYS_USER_ROLE_ENTITY.USER_ID))
            .leftJoin(BASE_SYS_ROLE_ENTITY)
            .on(BASE_SYS_USER_ROLE_ENTITY.ROLE_ID.eq(BASE_SYS_ROLE_ENTITY.ID))
            .leftJoin(BASE_SYS_DEPARTMENT_ENTITY)
            .on(BASE_SYS_USER_ENTITY.DEPARTMENT_ID.eq(BASE_SYS_DEPARTMENT_ENTITY.ID));

        // 不显示admin用户
        qw.and(BASE_SYS_USER_ENTITY.USERNAME.ne("admin"));
        // 筛选部门
        qw.and(BASE_SYS_USER_ENTITY.DEPARTMENT_ID.in(departmentIds,
            ArrayUtil.isNotEmpty(departmentIds)));
        // 筛选状态
        qw.and(BASE_SYS_USER_ENTITY.STATUS.eq(status, status != null));
        // 筛选角色
        qw.and(BASE_SYS_USER_ROLE_ENTITY.ROLE_ID.in(roleIds, ArrayUtil.isNotEmpty(roleIds)));
        // 搜索关键字
        if (StrUtil.isNotEmpty(keyWord)) {
            qw.and(BASE_SYS_USER_ENTITY.NAME.like(keyWord)
                .or(BASE_SYS_USER_ENTITY.USERNAME.like(keyWord))
                .or(BASE_SYS_USER_ENTITY.PHONE.like(keyWord)));
        }
        // 过滤部门权限
        qw.and(BASE_SYS_USER_ENTITY.DEPARTMENT_ID.in(
            permsDepartmentArr == null || permsDepartmentArr.length == 0 ? new Long[]{null}
                : permsDepartmentArr,
            !CoolSecurityUtil.getAdminUsername().equals("admin")));
        if (DatabaseDialectUtils.isPostgresql()) {
            // 兼容postgresql
            qw.groupBy("base_sys_user.id","base_sys_user.create_time","base_sys_user.department_id",
                "base_sys_user.email","base_sys_user.head_img","base_sys_user.name","base_sys_user.nick_name",
                "base_sys_user.phone","base_sys_user.remark","base_sys_user.status",
                "base_sys_user.update_time","base_sys_user.username",
                "base_sys_department.name");
        } else {
            qw.groupBy(BASE_SYS_USER_ENTITY.ID);
        }
        return mapper.paginate(page, qw);
    }

    @Override
    public void personUpdate(Long userId, Dict body) {
        BaseSysUserEntity userEntity = getById(userId);
        CoolPreconditions.checkEmpty(userEntity, "用户不存在");
        userEntity.setNickName(body.getStr("nickName"));
        userEntity.setHeadImg(body.getStr("headImg"));
        // 修改密码
        if (StrUtil.isNotEmpty(body.getStr("password"))) {
            userEntity.setPassword(MD5.create().digestHex(body.getStr("password")));
            userEntity.setPasswordV(userEntity.getPasswordV() + 1);
            coolCache.set("admin:passwordVersion:" + userId, userEntity.getPasswordV());
        }
        updateById(userEntity);
    }

    @Override
    public void move(Long departmentId, Long[] userIds) {
        UpdateChain.of(BaseSysUserEntity.class)
            .set(BaseSysUserEntity::getDepartmentId, departmentId)
            .in(BaseSysUserEntity::getId, (Object) userIds).update();
    }

    @Override
    public Long add(JSONObject requestParams, BaseSysUserEntity entity) {
        BaseSysUserEntity check = getOne(
            QueryWrapper.create().eq(BaseSysUserEntity::getUsername, entity.getUsername()));
        CoolPreconditions.check(check != null, "用户名已存在");
        entity.setPassword(MD5.create().digestHex(entity.getPassword()));
        super.add(requestParams, entity);
        return entity.getId();
    }

    @Override
    public boolean update(JSONObject requestParams, BaseSysUserEntity entity) {
        CoolPreconditions.check(
            StrUtil.isNotEmpty(entity.getUsername()) && entity.getUsername().equals("admin"),
            "非法操作");
        BaseSysUserEntity userEntity = getById(entity.getId());
        if (StrUtil.isNotEmpty(entity.getPassword())) {
            entity.setPasswordV(entity.getPasswordV() + 1);
            entity.setPassword(MD5.create().digestHex(entity.getPassword()));
            coolCache.set("admin:passwordVersion:" + entity.getId(), entity.getPasswordV());
        } else {
            entity.setPassword(userEntity.getPassword());
            entity.setPasswordV(userEntity.getPasswordV());
        }
        // 被禁用
        if (entity.getStatus() == 0) {
            CoolSecurityUtil.adminLogout(entity);
        }
        return super.update(requestParams, entity);
    }

    @Override
    public void modifyAfter(JSONObject requestParams, BaseSysUserEntity baseSysUserEntity,
        ModifyEnum type) {
        if (type != ModifyEnum.DELETE && requestParams.get("roleIdList", Long[].class) != null) {
            // 刷新权限
            baseSysPermsService.updateUserRole(baseSysUserEntity.getId(),
                requestParams.get("roleIdList", Long[].class));
        }
    }

    @Override
    public BaseSysUserEntity info(Long id) {
        BaseSysUserEntity userEntity = getById(id);
        Long[] roleIdList = baseSysPermsService.getRoles(id);
        BaseSysDepartmentEntity departmentEntity = baseSysDepartmentMapper.selectOneById(
            userEntity.getDepartmentId());
        userEntity.setPassword(null);
        
        // 安全处理roleIdList为null的情况
        if (roleIdList != null && roleIdList.length > 0) {
            userEntity.setRoleIdList(List.of(roleIdList));
        } else {
            userEntity.setRoleIdList(List.of()); // 返回空列表而不是null
        }
        userEntity.setDepartmentName(departmentEntity != null ? departmentEntity.getName() : userEntity.getDepartmentName());
        
        // 获取角色名称 - 使用简单的查询方式
        if (roleIdList != null && roleIdList.length > 0) {
            try {
                // 使用现有的查询方法获取角色名称
                QueryWrapper userWithRoleQuery = QueryWrapper.create()
                    .select(groupConcat(BASE_SYS_ROLE_ENTITY.NAME).as("roleName"))
                    .from(BASE_SYS_USER_ENTITY)
                    .leftJoin(BASE_SYS_USER_ROLE_ENTITY)
                    .on(BASE_SYS_USER_ENTITY.ID.eq(BASE_SYS_USER_ROLE_ENTITY.USER_ID))
                    .leftJoin(BASE_SYS_ROLE_ENTITY)
                    .on(BASE_SYS_USER_ROLE_ENTITY.ROLE_ID.eq(BASE_SYS_ROLE_ENTITY.ID))
                    .where(BASE_SYS_USER_ENTITY.ID.eq(id))
                    .groupBy(BASE_SYS_USER_ENTITY.ID);
                
                BaseSysUserEntity userWithRole = mapper.selectOneByQuery(userWithRoleQuery);
                if (userWithRole != null && userWithRole.getRoleName() != null) {
                    userEntity.setRoleName(userWithRole.getRoleName());
                }
            } catch (Exception e) {
                // 简化日志，避免log依赖问题
                System.err.println("获取用户" + id + "的角色名称失败: " + e.getMessage());
            }
        }
        
        return userEntity;
    }

    @Override
    public List<BaseSysUserEntity> queryUsers(UserQueryRequest request) {
        // 获取当前用户信息，用于数据权限控制
        Long currentUserId = CoolSecurityUtil.getCurrentUserId();
        String currentUsername = CoolSecurityUtil.getAdminUsername();
        boolean isAdmin = "admin".equals(currentUsername);
        
        // 获取用户的部门权限
        Long[] permsDepartmentArr = null;
        if (!isAdmin && currentUserId != null) {
            permsDepartmentArr = coolCache.get("admin:department:" + currentUserId, Long[].class);
        }
        
        QueryWrapper qw = QueryWrapper.create();
        
        // 根据请求参数决定查询字段
        if (request.getIncludeRoles() != null && request.getIncludeRoles()) {
            if (DatabaseDialectUtils.isPostgresql()) {
                // 兼容postgresql
                qw.select("base_sys_user.id","base_sys_user.create_time","base_sys_user.department_id",
                    "base_sys_user.email","base_sys_user.head_img","base_sys_user.name","base_sys_user.nick_name",
                    "base_sys_user.phone","base_sys_user.remark","base_sys_user.status",
                    "base_sys_user.update_time","base_sys_user.username",
                    "string_agg(base_sys_role.name, ', ') AS roleName",
                    "base_sys_department.name AS departmentName"
                );
            } else {
                qw.select(BASE_SYS_USER_ENTITY.ALL_COLUMNS,
                    groupConcat(BASE_SYS_ROLE_ENTITY.NAME).as("roleName"),
                    groupConcat(BASE_SYS_ROLE_ENTITY.ID).as("roleIds"),
                    groupConcat(BASE_SYS_ROLE_ENTITY.LABEL).as("roleLabels"),
                    BASE_SYS_DEPARTMENT_ENTITY.NAME.as("departmentName")
                );
            }
        } else {
            if (request.getIncludeDepartment() != null && request.getIncludeDepartment()) {
                qw.select(BASE_SYS_USER_ENTITY.ALL_COLUMNS,
                    BASE_SYS_DEPARTMENT_ENTITY.NAME.as("departmentName"));
            } else {
                qw.select(BASE_SYS_USER_ENTITY.ALL_COLUMNS);
            }
        }

        // 构建FROM和JOIN子句
        qw.from(BASE_SYS_USER_ENTITY);
        
        if (request.getIncludeRoles() != null && request.getIncludeRoles()) {
            qw.leftJoin(BASE_SYS_USER_ROLE_ENTITY)
                .on(BASE_SYS_USER_ENTITY.ID.eq(BASE_SYS_USER_ROLE_ENTITY.USER_ID))
                .leftJoin(BASE_SYS_ROLE_ENTITY)
                .on(BASE_SYS_USER_ROLE_ENTITY.ROLE_ID.eq(BASE_SYS_ROLE_ENTITY.ID));
        }
        
        if (request.getIncludeDepartment() != null && request.getIncludeDepartment()) {
            qw.leftJoin(BASE_SYS_DEPARTMENT_ENTITY)
                .on(BASE_SYS_USER_ENTITY.DEPARTMENT_ID.eq(BASE_SYS_DEPARTMENT_ENTITY.ID));
        }

        // 构建WHERE条件
        
        // 1. 数据权限控制
        if (!isAdmin && permsDepartmentArr != null && permsDepartmentArr.length > 0) {
            qw.and(BASE_SYS_USER_ENTITY.DEPARTMENT_ID.in((Object[]) permsDepartmentArr));
        }
        
        // 2. 排除admin用户
        if (request.getExcludeAdmin() != null && request.getExcludeAdmin()) {
            qw.and(BASE_SYS_USER_ENTITY.USERNAME.ne("admin"));
        }
        
        // 3. 用户状态筛选
        if (request.getStatus() != null) {
            qw.and(BASE_SYS_USER_ENTITY.STATUS.eq(request.getStatus()));
        }
        
        // 4. 部门筛选
        if (request.getDepartmentIds() != null && !request.getDepartmentIds().isEmpty()) {
            qw.and(BASE_SYS_USER_ENTITY.DEPARTMENT_ID.in(request.getDepartmentIds()));
        }
        
        // 5. 角色筛选
        if (request.getRoleIds() != null && !request.getRoleIds().isEmpty()) {
            qw.and(BASE_SYS_USER_ROLE_ENTITY.ROLE_ID.in(request.getRoleIds()));
        }
        
        if (request.getRoleNames() != null && !request.getRoleNames().isEmpty()) {
            qw.and(BASE_SYS_ROLE_ENTITY.NAME.in(request.getRoleNames()));
        }
        
        // 6. 模糊查询条件
        if (StrUtil.isNotEmpty(request.getKeyword())) {
            String kw = request.getKeyword();
            qw.and(BASE_SYS_USER_ENTITY.NAME.like(kw)
                .or(BASE_SYS_USER_ENTITY.USERNAME.like(kw))
                .or(BASE_SYS_USER_ENTITY.PHONE.like(kw)));
        } else {
            // 兼容旧参数
            if (StrUtil.isNotEmpty(request.getName())) {
                qw.and(BASE_SYS_USER_ENTITY.NAME.like(request.getName()));
            }

            if (StrUtil.isNotEmpty(request.getPhone())) {
                qw.and(BASE_SYS_USER_ENTITY.PHONE.like(request.getPhone()));
            }

            if (StrUtil.isNotEmpty(request.getUsername())) {
                qw.and(BASE_SYS_USER_ENTITY.USERNAME.like(request.getUsername()));
            }
        }
        
        // 邮箱单独处理
        if (StrUtil.isNotEmpty(request.getEmail())) {
            qw.and(BASE_SYS_USER_ENTITY.EMAIL.like(request.getEmail()));
        }
        
        // 7. 分组（如果包含角色信息）
        if (request.getIncludeRoles() != null && request.getIncludeRoles()) {
            if (DatabaseDialectUtils.isPostgresql()) {
                // 兼容postgresql
                qw.groupBy("base_sys_user.id","base_sys_user.create_time","base_sys_user.department_id",
                    "base_sys_user.email","base_sys_user.head_img","base_sys_user.name","base_sys_user.nick_name",
                    "base_sys_user.phone","base_sys_user.remark","base_sys_user.status",
                    "base_sys_user.update_time","base_sys_user.username",
                    "base_sys_department.name");
            } else {
                qw.groupBy(BASE_SYS_USER_ENTITY.ID);
            }
        }
        
        // 8. 排序
        if (StrUtil.isNotEmpty(request.getOrderBy())) {
            boolean isAsc = !"desc".equalsIgnoreCase(request.getOrderDirection());
            switch (request.getOrderBy()) {
                case "name":
                    qw.orderBy(BASE_SYS_USER_ENTITY.NAME, isAsc);
                    break;
                case "createTime":
                    qw.orderBy(BASE_SYS_USER_ENTITY.CREATE_TIME, isAsc);
                    break;
                case "departmentName":
                    qw.orderBy(BASE_SYS_DEPARTMENT_ENTITY.NAME, isAsc);
                    break;
                default:
                    qw.orderBy(BASE_SYS_USER_ENTITY.ID, isAsc);
            }
        } else {
            // 默认排序：按部门名称、姓名排序
            qw.orderBy(BASE_SYS_DEPARTMENT_ENTITY.NAME.asc(), BASE_SYS_USER_ENTITY.NAME.asc());
        }
        
        return mapper.selectListByQuery(qw);
    }

    @Override
    @Deprecated
    public List<BaseSysUserEntity> getAvailableAssignees() {
        QueryWrapper qw = QueryWrapper.create();
        
        if (DatabaseDialectUtils.isPostgresql()) {
            // 兼容postgresql
            qw.select("base_sys_user.id","base_sys_user.create_time","base_sys_user.department_id",
                "base_sys_user.email","base_sys_user.head_img","base_sys_user.name","base_sys_user.nick_name",
                "base_sys_user.phone","base_sys_user.remark","base_sys_user.status",
                "base_sys_user.update_time","base_sys_user.username",
                "string_agg(base_sys_role.name, ', ') AS roleName",
                "base_sys_department.name AS departmentName"
            );
        } else {
            qw.select(BASE_SYS_USER_ENTITY.ALL_COLUMNS,
                groupConcat(BASE_SYS_ROLE_ENTITY.NAME).as("roleName"),
                BASE_SYS_DEPARTMENT_ENTITY.NAME.as("departmentName")
            );
        }

        qw.from(BASE_SYS_USER_ENTITY).leftJoin(BASE_SYS_USER_ROLE_ENTITY)
            .on(BASE_SYS_USER_ENTITY.ID.eq(BASE_SYS_USER_ROLE_ENTITY.USER_ID))
            .leftJoin(BASE_SYS_ROLE_ENTITY)
            .on(BASE_SYS_USER_ROLE_ENTITY.ROLE_ID.eq(BASE_SYS_ROLE_ENTITY.ID))
            .leftJoin(BASE_SYS_DEPARTMENT_ENTITY)
            .on(BASE_SYS_USER_ENTITY.DEPARTMENT_ID.eq(BASE_SYS_DEPARTMENT_ENTITY.ID));

        // 只查询正常状态的用户
        qw.where(BASE_SYS_USER_ENTITY.STATUS.eq(1));
        // 不显示admin用户
        qw.and(BASE_SYS_USER_ENTITY.USERNAME.ne("admin"));
        
        if (DatabaseDialectUtils.isPostgresql()) {
            // 兼容postgresql
            qw.groupBy("base_sys_user.id","base_sys_user.create_time","base_sys_user.department_id",
                "base_sys_user.email","base_sys_user.head_img","base_sys_user.name","base_sys_user.nick_name",
                "base_sys_user.phone","base_sys_user.remark","base_sys_user.status",
                "base_sys_user.update_time","base_sys_user.username",
                "base_sys_department.name");
        } else {
            qw.groupBy(BASE_SYS_USER_ENTITY.ID);
        }
        
        // 按部门和姓名排序
        qw.orderBy(BASE_SYS_DEPARTMENT_ENTITY.NAME.asc(), BASE_SYS_USER_ENTITY.NAME.asc());
        
        return mapper.selectListByQuery(qw);
    }
}