package com.cool.modules.project.entity;

import com.cool.core.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 项目实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table(value = "project", comment = "项目表")
public class ProjectEntity extends BaseEntity<ProjectEntity> {
    @ColumnDefine(comment = "项目编码", length = 50)
    private String code;
    @ColumnDefine(comment = "项目编码1", length = 50)
    private String code1;
    @ColumnDefine(comment = "项目名称", length = 100)
    private String name;
    @ColumnDefine(comment = "项目别名", length = 100)
    private String alias;
    @ColumnDefine(comment = "项目经理", length = 50)
    private String manager;
    @ColumnDefine(comment = "自有员工数量")
    private Integer staffCount;
    @ColumnDefine(comment = "外包员工数量")
    private Integer outsourceCount;
    @ColumnDefine(comment = "所属区域名称", length = 100)
    private String belongDeptName;
    @ColumnDefine(comment = "所属城区名称", length = 100)
    private String belongCityName;
    @ColumnDefine(comment = "所在省", length = 50)
    private String province;
    @ColumnDefine(comment = "所在市", length = 50)
    private String city;
    @ColumnDefine(comment = "所在区", length = 50)
    private String district;
    @ColumnDefine(comment = "街道/镇", length = 100)
    private String street;
    @ColumnDefine(comment = "目标城市等级", length = 20)
    private String cityLevel;
    @ColumnDefine(comment = "详细地址", length = 255)
    private String address;
    @ColumnDefine(comment = "经度", type = "decimal(10,6)")
    private Double longitude;
    @ColumnDefine(comment = "纬度", type = "decimal(10,6)")
    private Double latitude;
    @ColumnDefine(comment = "职场级别", length = 50)
    private String jobLevel;
    @ColumnDefine(comment = "主业态（一级）", length = 50)
    private String mainType1;
    @ColumnDefine(comment = "主业态（二级）", length = 50)
    private String mainType2;
    @ColumnDefine(comment = "业委会成立情况", length = 20)
    private String established;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ColumnDefine(comment = "首次签约时间")
    private LocalDate firstSignDate;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ColumnDefine(comment = "实际进场时间")
    private LocalDate actualEntryDate;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ColumnDefine(comment = "关键能耗收费时间")
    private LocalDate publicRevenueDate;
    @ColumnDefine(comment = "是否合资公司项目", type = "tinyint")
    private Integer isFundProject;
    @ColumnDefine(comment = "项目生命周期", length = 50)
    private String projectLifeCycle;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ColumnDefine(comment = "撤场日期")
    private LocalDate exitDate;
    @ColumnDefine(comment = "撤场原因", length = 100)
    private String exitReason;
    @ColumnDefine(comment = "所属管理处名称", length = 100)
    private String mgmtOfficeName;
    @ColumnDefine(comment = "所属管理处编码", length = 50)
    private String mgmtOfficeCode;
    @ColumnDefine(comment = "运营状态", length = 20)
    private String operateStatus;
    @ColumnDefine(comment = "产权属性", length = 20)
    private String propertyRight;
    @ColumnDefine(comment = "收费方式", length = 20)
    private String chargeMode;
    @ColumnDefine(comment = "收费标准（m²/月）", length = 100)
    private String chargeStandard;
    @ColumnDefine(comment = "物业类型", length = 50)
    private String propertyType;
    @ColumnDefine(comment = "是否共融项目", type = "tinyint")
    private Integer isJointProject;
    @ColumnDefine(comment = "总房产建筑面积", type = "decimal(12,2)")
    private Double totalBuildArea;
    @ColumnDefine(comment = "收费面积", type = "decimal(12,2)")
    private Double chargeArea;
    @ColumnDefine(comment = "户数")
    private Integer householdCount;
    @ColumnDefine(comment = "总建筑面积（m²）", type = "decimal(12,2)")
    private Double totalArea;
    @ColumnDefine(comment = "占地面积（m²）", type = "decimal(12,2)")
    private Double landArea;
    @ColumnDefine(comment = "地下室建筑面积（m²）", type = "decimal(12,2)")
    private Double undergroundArea;
    @ColumnDefine(comment = "绿化面积（m²）", type = "decimal(12,2)")
    private Double greenArea;
    @ColumnDefine(comment = "容积率", type = "decimal(5,2)")
    private Double plotRatio;
    @ColumnDefine(comment = "是否有停车场", type = "tinyint")
    private Integer hasParking;
    @ColumnDefine(comment = "总车位个数")
    private Integer totalParkingCount;
    @ColumnDefine(comment = "停车场是否自管", length = 10)
    private String selfParkingMgmt;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ColumnDefine(comment = "室内停车场收费时间")
    private LocalDate indoorParkingDate;
    @ColumnDefine(comment = "出入口数量（个）")
    private Integer entranceCount;
    @ColumnDefine(comment = "门控室数量（个）")
    private Integer accessCount;
    @ColumnDefine(comment = "消防控制室数量（个）")
    private Integer fireControlCount;
    @ColumnDefine(comment = "升降系统数量（个）")
    private Integer elevatorCount;
    @ColumnDefine(comment = "中央空调主机数量（个）")
    private Integer centralAcCount;
    @ColumnDefine(comment = "供暖锅炉数量（个）")
    private Integer boilerCount;
    @ColumnDefine(comment = "审批状态", length = 20)
    private String approvalStatus;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ColumnDefine(comment = "开始时间")
    private LocalDateTime startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ColumnDefine(comment = "结束时间")
    private LocalDateTime endTime;
} 