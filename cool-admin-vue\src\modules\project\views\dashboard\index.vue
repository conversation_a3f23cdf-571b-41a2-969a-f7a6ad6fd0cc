<template>
  <div class="project-dashboard">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <h1 class="page-title">项目工作台</h1>
        <p class="page-description">管理和跟踪您参与的所有项目</p>
      </div>
      
      <div class="header-actions">
        <el-button type="primary" @click="showCreateProject = true">
          <el-icon><Plus /></el-icon>
          新建项目
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon total">
            <el-icon><FolderOpened /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.totalCount || 0 }}</div>
            <div class="stat-label">总项目数</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon progress">
            <el-icon><Loading /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.statusStats?.inProgress || 0 }}</div>
            <div class="stat-label">进行中</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon completed">
            <el-icon><Check /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.statusStats?.completed || 0 }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon urgent">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.priorityStats?.urgent || 0 }}</div>
            <div class="stat-label">紧急项目</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 项目列表 -->
    <div class="project-list-section">
      <div class="section-header">
        <h2>我的项目</h2>
        <div class="section-actions">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索项目..."
            style="width: 200px"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px" @change="handleFilter">
            <el-option label="全部" value="" />
            <el-option label="规划中" :value="0" />
            <el-option label="进行中" :value="1" />
            <el-option label="已完成" :value="2" />
            <el-option label="已暂停" :value="3" />
          </el-select>
        </div>
      </div>

      <div class="project-grid">
        <div
          v-for="project in filteredProjects"
          :key="project.id"
          class="project-card"
          @click="goToProject(project)"
        >
          <div class="project-header">
            <div class="project-title">{{ project.projectName }}</div>
            <el-tag
              :type="getStatusTagType(project.status)"
              size="small"
              effect="plain"
            >
              {{ getStatusText(project.status) }}
            </el-tag>
          </div>

          <div class="project-description">
            {{ project.description || '暂无描述' }}
          </div>

          <div class="project-meta">
            <div class="meta-item">
              <el-icon><User /></el-icon>
              <span>{{ project.ownerName || '未指定' }}</span>
            </div>
            <div class="meta-item">
              <el-icon><Calendar /></el-icon>
              <span>{{ formatDate(project.createTime) }}</span>
            </div>
          </div>

          <div class="project-progress">
            <div class="progress-info">
              <span>完成进度</span>
              <span>{{ project.completionRate || 0 }}%</span>
            </div>
            <el-progress
              :percentage="project.completionRate || 0"
              :stroke-width="6"
              :show-text="false"
            />
          </div>

          <div class="project-footer">
            <div class="project-stats">
              <span class="stat-item">
                <el-icon><User /></el-icon>
                {{ project.memberCount || 0 }}人
              </span>
              <span class="stat-item">
                <el-icon><Document /></el-icon>
                {{ project.taskCount || 0 }}任务
              </span>
            </div>
            
            <el-dropdown @command="handleProjectAction">
              <el-button type="text" size="small">
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{ action: 'edit', project }">编辑</el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'members', project }">成员管理</el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'archive', project }" divided>归档</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredProjects.length === 0" class="empty-state">
          <el-empty description="暂无项目数据">
            <el-button type="primary" @click="showCreateProject = true">创建第一个项目</el-button>
          </el-empty>
        </div>
      </div>
    </div>

    <!-- 创建项目对话框 -->
    <project-create-dialog
      v-model="showCreateProject"
      @success="handleCreateSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import {
  Plus, FolderOpened, Loading, Check, Warning, Search,
  User, Calendar, Document, MoreFilled
} from '@element-plus/icons-vue';
import { service } from '/@/cool';
import { useOrganizationStore } from '../../organization/store/organization';

// 路由
const router = useRouter();

// Store
const organizationStore = useOrganizationStore();

// 响应式数据
const loading = ref(false);
const showCreateProject = ref(false);
const searchKeyword = ref('');
const statusFilter = ref('');

const projects = ref<any[]>([]);
const statistics = ref<any>({});

// 计算属性
const filteredProjects = computed(() => {
  let result = projects.value;

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(project =>
      project.projectName.toLowerCase().includes(keyword) ||
      (project.description && project.description.toLowerCase().includes(keyword))
    );
  }

  // 状态筛选
  if (statusFilter.value !== '') {
    result = result.filter(project => project.status === statusFilter.value);
  }

  return result;
});

// 方法
const loadProjects = async () => {
  try {
    loading.value = true;
    const result = await service.request({
      url: '/admin/project/info/my-projects',
      method: 'GET'
    });

    if (result.code === 1000) {
      projects.value = result.data || [];
    }
  } catch (error) {
    console.error('加载项目列表失败:', error);
    ElMessage.error('加载项目列表失败');
  } finally {
    loading.value = false;
  }
};

const loadStatistics = async () => {
  try {
    const result = await service.request({
      url: '/admin/project/info/statistics',
      method: 'GET'
    });

    if (result.code === 1000) {
      statistics.value = result.data || {};
    }
  } catch (error) {
    console.error('加载统计数据失败:', error);
  }
};

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
};

const handleFilter = () => {
  // 筛选逻辑已在计算属性中处理
};

const goToProject = (project: any) => {
  router.push(`/project/detail/${project.id}`);
};

const handleProjectAction = ({ action, project }: any) => {
  switch (action) {
    case 'edit':
      // 编辑项目
      break;
    case 'members':
      router.push(`/project/member/${project.id}`);
      break;
    case 'archive':
      // 归档项目
      break;
  }
};

const handleCreateSuccess = () => {
  loadProjects();
  loadStatistics();
};

const getStatusTagType = (status: number) => {
  const types = ['info', 'success', 'success', 'warning', 'danger'];
  return types[status] || 'info';
};

const getStatusText = (status: number) => {
  const texts = ['规划中', '进行中', '已完成', '已暂停', '已取消'];
  return texts[status] || '未知';
};

const formatDate = (date: string) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString();
};

// 生命周期
onMounted(() => {
  loadProjects();
  loadStatistics();
});
</script>

<style lang="scss" scoped>
.project-dashboard {
  padding: 20px;

  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;

    .header-content {
      .page-title {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }

      .page-description {
        margin: 0;
        color: var(--el-text-color-secondary);
      }
    }
  }

  .stats-cards {
    margin-bottom: 32px;

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;

      .stat-card {
        display: flex;
        align-items: center;
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          font-size: 20px;

          &.total {
            background: var(--el-color-primary-light-9);
            color: var(--el-color-primary);
          }

          &.progress {
            background: var(--el-color-warning-light-9);
            color: var(--el-color-warning);
          }

          &.completed {
            background: var(--el-color-success-light-9);
            color: var(--el-color-success);
          }

          &.urgent {
            background: var(--el-color-danger-light-9);
            color: var(--el-color-danger);
          }
        }

        .stat-content {
          .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            line-height: 1;
          }

          .stat-label {
            font-size: 14px;
            color: var(--el-text-color-secondary);
            margin-top: 4px;
          }
        }
      }
    }
  }

  .project-list-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
      }

      .section-actions {
        display: flex;
        gap: 12px;
      }
    }

    .project-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 20px;

      .project-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
          transform: translateY(-2px);
        }

        .project-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;

          .project-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            flex: 1;
            margin-right: 12px;
          }
        }

        .project-description {
          color: var(--el-text-color-secondary);
          font-size: 14px;
          line-height: 1.5;
          margin-bottom: 16px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .project-meta {
          display: flex;
          gap: 16px;
          margin-bottom: 16px;

          .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: var(--el-text-color-secondary);

            .el-icon {
              font-size: 14px;
            }
          }
        }

        .project-progress {
          margin-bottom: 16px;

          .progress-info {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: var(--el-text-color-secondary);
            margin-bottom: 8px;
          }
        }

        .project-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .project-stats {
            display: flex;
            gap: 12px;

            .stat-item {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              color: var(--el-text-color-secondary);

              .el-icon {
                font-size: 14px;
              }
            }
          }
        }
      }

      .empty-state {
        grid-column: 1 / -1;
        text-align: center;
        padding: 40px;
      }
    }
  }
}
</style>
