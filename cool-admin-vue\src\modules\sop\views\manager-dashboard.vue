<template>
  <el-scrollbar>
    <div class="manager-dashboard-page">
      <!-- 统计概览 - 移到最前面 -->
      <div class="stats-section">
        <div class="section-header">
          <h3>📊 管理数据概览</h3>
          <p>全局关键指标一览</p>
        </div>
        <el-row :gutter="24">
          <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
            <div class="stat-card stat-card-1">
              <div class="stat-icon">🏢</div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.scenarios }}</div>
                <div class="stat-label">总场景数</div>
              </div>
              <div class="stat-trend">
                <el-icon class="trend-icon"><ArrowUp /></el-icon>
                <span>+2</span>
              </div>
            </div>
          </el-col>

          <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
            <div class="stat-card stat-card-2">
              <div class="stat-icon">⚡</div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.activeTasks }}</div>
                <div class="stat-label">活跃任务</div>
              </div>
              <div class="stat-trend">
                <el-icon class="trend-icon"><ArrowUp /></el-icon>
                <span>+5</span>
              </div>
            </div>
          </el-col>

          <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
            <div class="stat-card stat-card-3">
              <div class="stat-icon">✅</div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.completedTasks }}</div>
                <div class="stat-label">已完成任务</div>
              </div>
              <div class="stat-trend">
                <el-icon class="trend-icon"><ArrowUp /></el-icon>
                <span>+12</span>
              </div>
            </div>
          </el-col>

          <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
            <div class="stat-card stat-card-4">
              <div class="stat-icon">📚</div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.templates }}</div>
                <div class="stat-label">模板数量</div>
              </div>
              <div class="stat-trend">
                <el-icon class="trend-icon"><ArrowUp /></el-icon>
                <span>+1</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 快捷操作 -->
      <div class="quick-actions">
        <div class="section-header">
          <h3>🚀 管理操作</h3>
          <p>一键启动各种管理功能模块</p>
        </div>
        <el-row :gutter="24">
          <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
            <div class="action-card ai-card" @click="goToAIGenerator">
              <div class="card-background">
                <div class="bg-circle bg-circle-1"></div>
                <div class="bg-circle bg-circle-2"></div>
              </div>
              <div class="action-content">
                <div class="action-icon ai-icon">
                  <span class="icon-text">🎯</span>
                  <div class="icon-glow"></div>
                </div>
                <h4>AI生成SOP</h4>
                <p>自然语言描述，智能生成标准作业程序</p>
                <el-button type="primary" class="action-btn ai-btn">
                  <el-icon><Star /></el-icon>
                  开始生成
                </el-button>
              </div>
            </div>
          </el-col>

          <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
            <div class="action-card scenario-card" @click="goToScenarios">
              <div class="action-content">
                <div class="action-icon scenario-icon">
                  📋
                </div>
                <h4>场景管理</h4>
                <p>管理和配置各种SOP业务场景</p>
                <el-button class="action-btn scenario-btn">
                  <el-icon><Setting /></el-icon>
                  进入管理
                </el-button>
              </div>
            </div>
          </el-col>

          <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
            <div class="action-card task-card" @click="goToTasks">
              <div class="action-content">
                <div class="action-icon task-icon">
                  ✅
                </div>
                <h4>任务中心</h4>
                <p>查看和管理所有生成的任务</p>
                <el-button class="action-btn task-btn">
                  <el-icon><List /></el-icon>
                  查看任务
                </el-button>
              </div>
            </div>
          </el-col>

          <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
            <div class="action-card template-card" @click="goToTemplates">
              <div class="action-content">
                <div class="action-icon template-icon">
                  📝
                </div>
                <h4>模板库</h4>
                <p>浏览和使用标准化模板</p>
                <el-button class="action-btn template-btn">
                  <el-icon><Document /></el-icon>
                  浏览模板
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 最新动态 -->
      <div class="timeline-section">
        <div class="section-header">
          <h3>📈 系统动态</h3>
          <p>最新的系统更新和重要事件</p>
        </div>
        <div class="timeline-card">
          <el-timeline>
            <el-timeline-item
              timestamp="刚刚"
              :icon="Check"
              color="#67c23a"
            >
              <div class="timeline-content">
                <h4>任务步骤流程组件优化完成</h4>
                <p>提升用户体验和操作效率</p>
              </div>
            </el-timeline-item>

            <el-timeline-item
              timestamp="1.5天前"
              :icon="Tools"
              color="#8b5cf6"
            >
              <div class="timeline-content">
                <h4>系统整体架构优化完成</h4>
                <p>提升系统性能和稳定性</p>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import { Star, Setting, List, Document, Check, ArrowUp, Tools } from "@element-plus/icons-vue";

defineOptions({
  name: "sop-manager-dashboard"
});

const router = useRouter();

// 统计数据（模拟数据，实际应该从后端获取）
const stats = ref({
  scenarios: 12,
  activeTasks: 8,
  completedTasks: 45,
  templates: 6
});

// 快捷操作方法
const goToAIGenerator = () => {
  router.push("/sop/ai-generator");
};

const goToScenarios = () => {
  router.push("/sop/scenario");
};

const goToTasks = () => {
  router.push("/task/info");
};

const goToTemplates = () => {
  router.push("/sop/template");
};
</script>

<style scoped>
.manager-dashboard-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 60px);
}

/* 统计概览区域 */
.stats-section {
  margin-bottom: 32px;
}

/* 迷你欢迎区域 */
.welcome-section-mini {
  margin-bottom: 32px;
}

.welcome-content-mini {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 24px;
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-dark-2) 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0 4px 20px rgba(var(--el-color-primary-rgb), 0.2);
  transition: all 0.3s ease;
}

.welcome-content-mini:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(var(--el-color-primary-rgb), 0.3);
}

.welcome-icon {
  font-size: 32px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.welcome-text h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
}

.welcome-text p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

/* 通用区域样式 */
.section-header {
  margin-bottom: 24px;
  text-align: center;
}

.section-header h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.section-header p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

/* 统计卡片样式 */
.stat-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  background: rgb(96, 78, 178);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  height: 140px;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-success));
}

.stat-card-1::before {
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.stat-card-2::before {
  background: linear-gradient(90deg, #f093fb, #f5576c);
}

.stat-card-3::before {
  background: linear-gradient(90deg, #4facfe, #00f2fe);
}

.stat-card-4::before {
  background: linear-gradient(90deg, #43e97b, #38f9d7);
}

.stat-icon {
  font-size: 36px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(var(--el-color-primary-rgb), 0.1) 0%, rgba(var(--el-color-primary-rgb), 0.05) 100%);
}

.stat-content {
  flex: 1;
  margin-left: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--el-color-success);
  font-size: 14px;
  font-weight: 600;
}

.trend-icon {
  font-size: 16px;
}

/* 快捷操作区域 */
.quick-actions {
  margin-bottom: 32px;
}

.action-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  cursor: pointer;
  height: 360px;
  position: relative;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
}

.action-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
}

.bg-circle-1 {
  width: 120px;
  height: 120px;
  top: -60px;
  right: -60px;
}

.bg-circle-2 {
  width: 80px;
  height: 80px;
  bottom: -40px;
  left: -40px;
}

.action-content {
  position: relative;
  z-index: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 32px 32px;
  gap: 20px;
}

.action-icon {
  font-size: 48px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  position: relative;
}

.ai-icon {
  position: relative;
}

.icon-text {
  position: relative;
  z-index: 2;
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(var(--el-color-primary-rgb), 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.5;
  }
  100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
}

.action-card:hover .action-icon {
  transform: scale(1.1) rotate(5deg);
}

.action-content h4 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.action-content p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.6;
  min-height: 60px;
  display: flex;
  align-items: center;
}

.action-btn {
  margin-top: auto;
  width: 100%;
  height: 48px;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.ai-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.ai-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.ai-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.scenario-card {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.scenario-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.scenario-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.task-card {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.task-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.task-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.template-card {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.template-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.template-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* 时间线区域 */
.timeline-section {
  margin-bottom: 32px;
}

.timeline-card {
  border-radius: 16px;
  padding: 32px;
  background-color: #4f3145;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.timeline-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.timeline-content h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.timeline-content p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}
</style>