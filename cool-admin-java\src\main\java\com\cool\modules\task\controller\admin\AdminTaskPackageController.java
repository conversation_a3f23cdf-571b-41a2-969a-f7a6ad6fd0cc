package com.cool.modules.task.controller.admin;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.core.request.R;
import com.cool.modules.task.entity.TaskPackageEntity;
import com.cool.modules.task.service.TaskPackageService;
import com.mybatisflex.core.paginate.Page;

import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 场景任务包管理
 */
@Tag(name = "场景任务包管理", description = "场景任务包管理")
@CoolRestController(api = {"add", "delete", "update", "info", "list", "page"})
public class AdminTaskPackageController extends BaseController<TaskPackageService, TaskPackageEntity> {

    @Autowired
    private TaskPackageService taskPackageService;

    /**
     * 获取场景任务包列表（分页）
     */
    @Operation(summary = "获取场景任务包列表")
    @PostMapping("/scenarioPackages")
    public R scenarioPackages(@RequestBody Map<String, Object> params) {
        Page<TaskPackageEntity> page = new Page<>(
            (Integer) params.getOrDefault("page", 1),
            (Integer) params.getOrDefault("size", 20)
        );
        
        Page<TaskPackageEntity> result = taskPackageService.getScenarioPackages(page, params);
        return R.ok(result);
    }

    /**
     * 获取任务包详情（包含统计信息）
     */
    @Operation(summary = "获取任务包详情")
    @GetMapping("/detail/{packageId}")
    public R getPackageDetail(@PathVariable Long packageId) {
        TaskPackageEntity detail = taskPackageService.getPackageDetailWithStats(packageId);
        return R.ok(detail);
    }

    /**
     * 更新任务包统计信息
     */
    @Operation(summary = "更新任务包统计信息")
    @PostMapping("/updateStats/{packageId}")
    public R updatePackageStats(@PathVariable Long packageId) {
        taskPackageService.updatePackageStats(packageId);
        return R.ok("统计信息更新成功");
    }

    /**
     * 完成任务包
     */
    @Operation(summary = "完成任务包")
    @PostMapping("/complete/{packageId}")
    public R completePackage(@PathVariable Long packageId) {
        boolean success = taskPackageService.completePackage(packageId);
        if (success) {
            return R.ok("任务包已完成");
        } else {
            return R.error("完成任务包失败");
        }
    }

    /**
     * 关闭任务包
     */
    @Operation(summary = "关闭任务包")
    @PostMapping("/close/{packageId}")
    public R closePackage(@PathVariable Long packageId) {
        boolean success = taskPackageService.closePackage(packageId);
        if (success) {
            return R.ok("任务包已关闭");
        } else {
            return R.error("关闭任务包失败");
        }
    }

    /**
     * 根据场景ID获取任务包列表
     */
    @Operation(summary = "根据场景ID获取任务包列表")
    @GetMapping("/byScenario/{scenarioId}")
    public R getPackagesByScenarioId(@PathVariable Long scenarioId) {
        return R.ok(taskPackageService.getPackagesByScenarioId(scenarioId));
    }

    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
     
    }
}
