---
alwaysApply: true
---

# Cool Admin CURD最佳实践规范

## 1. Entity（实体类）规范
- 实体类放在 `modules/[module]/entity/`，类名以 `Entity` 结尾。
- 继承 `BaseEntity`，用 `@Table` 注解指定表名和注释。
- import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
- import com.mybatisflex.annotation.Table;
- 字段用 `@ColumnDefine` 注解，注明注释、类型、长度、默认值。
- 不需要手写建表SQL，Cool Admin会自动建表。
- 示例：
```java

@Table(value = "demo_example", comment = "示例表")
public class DemoExampleEntity extends BaseEntity<DemoExampleEntity> {
    @ColumnDefine(comment = "名称", length = 100)
    private String name;
    @ColumnDefine(comment = "状态", type = "tinyint", defaultValue = "1")
    private Integer status;
}
```

## 2. Mapper规范
- Mapper接口放在 `modules/[module]/mapper/`。
- 继承 `BaseMapper<Entity>`，无需多余代码。
- 示例：
```java
public interface DemoExampleMapper extends BaseMapper<DemoExampleEntity> {}
```

## 3. Service规范
- Service接口放在 `modules/[module]/service/`，实现放在 `service/impl/`。
- Service接口继承 `BaseService<Entity>`，实现继承 `BaseServiceImpl<Mapper, Entity>`。
- 用 `@Service` 注解。
- 示例：
```java
public interface DemoExampleService extends BaseService<DemoExampleEntity> {}

@Service
public class DemoExampleServiceImpl extends BaseServiceImpl<DemoExampleMapper, DemoExampleEntity> implements DemoExampleService {}
```

## 4. Controller规范
- Controller放在 `modules/[module]/controller/admin/`。
- 继承 `BaseController<Service, Entity>`，用 `@CoolRestController` 注解，自动注册标准CURD接口。
- 用 `@Tag` 注解标注接口分组和描述。
- 如需自定义查询条件，重写 `init` 方法，推荐用 TableDef 静态字段链式配置 `fieldEq` 和 `keyWordLikeFields`，并规范import。
- 示例：
```java
import com.cool.modules.demo.entity.table.DemoExampleEntityTableDef;

@Tag(name = "示例管理", description = "示例模块统一管理")
@CoolRestController(api = { "add", "delete", "update", "info", "page" })
public class AdminDemoExampleController extends BaseController<DemoExampleService, DemoExampleEntity> {
    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        setListOption(
            createOp()
                .fieldEq(DemoExampleEntityTableDef.DEMO_EXAMPLE_ENTITY.STATUS, DemoExampleEntityTableDef.DEMO_EXAMPLE_ENTITY.NAME)
                .keyWordLikeFields(DemoExampleEntityTableDef.DEMO_EXAMPLE_ENTITY.NAME, DemoExampleEntityTableDef.DEMO_EXAMPLE_ENTITY.DESCRIPTION)
        );
    }
}
```
- 说明：
  - 推荐用 TableDef 自动生成的静态字段，类型安全，IDE可提示。
  - `fieldEq`、`keyWordLikeFields` 支持链式调用，字段顺序与业务需求一致。
  - import 路径需指向 `entity.table.[Entity]TableDef`。
  - 如无TableDef可临时用字符串字段名，但建议补全自动生成。

## 5. EPS自动化与前后端联动
- 实体类、Controller、Service、Mapper结构规范后，EPS可自动生成前端service路径，无需手动创建service文件。
- 实体类变更后需重新生成EPS，前端服务路径自动同步。

## 6. 前端页面规范与技巧
- 页面放在 `src/modules/[module]/views/`，如 `views/example.vue`。
- 使用 `<cl-crud ref="Crud">` 包裹，`<cl-table ref="Table" />` 和 `<cl-upsert ref="Upsert" />`，不要用v-bind。
- CRUD配置用 `useCrud`，表格用 `useTable`，表单用 `useUpsert`。
- service路径严格用EPS自动生成的 `service.[module].[entity]`。
- 字段、label、类型、必填项与后端实体保持一致。
- 组件名用 `defineOptions({ name: "模块-页面" })`，便于路由缓存。
- 示例：
```vue
<template>
  <cl-crud ref="Crud">
    <cl-row>
      <cl-table ref="Table" />
      <cl-upsert ref="Upsert" />
    </cl-row>
  </cl-crud>
</template>
<script setup lang="ts">
import { useCrud, useTable, useUpsert } from "@cool-vue/crud";
import service from "@/cool/service";
const Crud = useCrud({
  service: service.demo.example,
  upsert: {
    columns: [
      { prop: "name", label: "名称", required: true },
      { prop: "status", label: "状态", type: "switch", dicData: [
        { label: "启用", value: 1 },
        { label: "禁用", value: 0 }
      ]}
    ]
  }
});
const Table = useTable({
  columns: [
    { prop: "name", label: "名称" },
    { prop: "status", label: "状态", type: "tag", dicData: [
      { label: "启用", value: 1, type: "success" },
      { label: "禁用", value: 0, type: "danger" }
    ]}
  ]
});
</script>
<defineOptions>{ name: "demo-example" }</defineOptions>
```

## 7. 常见技巧与注意事项
- 实体类、接口、页面字段命名保持一致，避免前后端不匹配。
- 返回结构统一用 `R.ok()`、`R.error()`，禁止返回Map。
- 注释需覆盖类、字段、方法，风格与官方一致。
- 包路径、命名、注解、继承、接口风格全部合规，便于低代码开发和团队协作。
- 如需自定义操作，参考task、demo模块扩展写法。





# Cool Admin CURD最佳实践规范

## 1. Entity（实体类）规范
- 实体类放在 `modules/[module]/entity/`，类名以 `Entity` 结尾。
- 继承 `BaseEntity`，用 `@Table` 注解指定表名和注释。
- import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
- 字段用 `@ColumnDefine` 注解，注明注释、类型、长度、默认值。
- 不需要手写建表SQL，Cool Admin会自动建表。
- 示例：
```java
@Table(value = "demo_example", comment = "示例表")
public class DemoExampleEntity extends BaseEntity<DemoExampleEntity> {
    @ColumnDefine(comment = "名称", length = 100)
    private String name;
    @ColumnDefine(comment = "状态", type = "tinyint", defaultValue = "1")
    private Integer status;
}
```

## 2. Mapper规范
- Mapper接口放在 `modules/[module]/mapper/`。
- 继承 `BaseMapper<Entity>`，无需多余代码。
- 示例：
```java
public interface DemoExampleMapper extends BaseMapper<DemoExampleEntity> {}
```

## 3. Service规范
- Service接口放在 `modules/[module]/service/`，实现放在 `service/impl/`。
- Service接口继承 `BaseService<Entity>`，实现继承 `BaseServiceImpl<Mapper, Entity>`。
- 用 `@Service` 注解。
- 示例：
```java
public interface DemoExampleService extends BaseService<DemoExampleEntity> {}

@Service
public class DemoExampleServiceImpl extends BaseServiceImpl<DemoExampleMapper, DemoExampleEntity> implements DemoExampleService {}
```

## 4. Controller规范
- Controller放在 `modules/[module]/controller/admin/`。
- 继承 `BaseController<Service, Entity>`，用 `@CoolRestController` 注解，自动注册标准CURD接口。
- 用 `@Tag` 注解标注接口分组和描述。
- 如需自定义查询条件，重写 `init` 方法，推荐用 TableDef 静态字段链式配置 `fieldEq` 和 `keyWordLikeFields`，并规范import。
- 示例：
```java
import com.cool.modules.demo.entity.table.DemoExampleEntityTableDef;

@Tag(name = "示例管理", description = "示例模块统一管理")
@CoolRestController(api = { "add", "delete", "update", "info", "page" })
public class AdminDemoExampleController extends BaseController<DemoExampleService, DemoExampleEntity> {
    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        setListOption(
            createOp()
                .fieldEq(DemoExampleEntityTableDef.DEMO_EXAMPLE_ENTITY.STATUS, DemoExampleEntityTableDef.DEMO_EXAMPLE_ENTITY.NAME)
                .keyWordLikeFields(DemoExampleEntityTableDef.DEMO_EXAMPLE_ENTITY.NAME, DemoExampleEntityTableDef.DEMO_EXAMPLE_ENTITY.DESCRIPTION)
        );
    }
}
```
- 说明：
  - 推荐用 TableDef 自动生成的静态字段，类型安全，IDE可提示。
  - `fieldEq`、`keyWordLikeFields` 支持链式调用，字段顺序与业务需求一致。
  - import 路径需指向 `entity.table.[Entity]TableDef`。
  - 如无TableDef可临时用字符串字段名，但建议补全自动生成。

## 5. EPS自动化与前后端联动
- 实体类、Controller、Service、Mapper结构规范后，EPS可自动生成前端service路径，无需手动创建service文件。
- 实体类变更后需重新生成EPS，前端服务路径自动同步。

## 6. 前端页面规范与技巧
- 页面放在 `src/modules/[module]/views/`，如 `views/example.vue`。
- 使用 `<cl-crud ref="Crud">` 包裹，`<cl-table ref="Table" />` 和 `<cl-upsert ref="Upsert" />`，不要用v-bind。
- CRUD配置用 `useCrud`，表格用 `useTable`，表单用 `useUpsert`。
- service路径严格用EPS自动生成的 `service.[module].[entity]`。
- 字段、label、类型、必填项与后端实体保持一致。
- 组件名用 `defineOptions({ name: "模块-页面" })`，便于路由缓存。
- 示例：
```vue
<template>
  <cl-crud ref="Crud">
    <cl-row>
      <cl-table ref="Table" />
      <cl-upsert ref="Upsert" />
    </cl-row>
  </cl-crud>
</template>
<script setup lang="ts">
import { useCrud, useTable, useUpsert } from "@cool-vue/crud";
import service from "@/cool/service";
const Crud = useCrud({
  service: service.demo.example,
  upsert: {
    columns: [
      { prop: "name", label: "名称", required: true },
      { prop: "status", label: "状态", type: "switch", dicData: [
        { label: "启用", value: 1 },
        { label: "禁用", value: 0 }
      ]}
    ]
  }
});
const Table = useTable({
  columns: [
    { prop: "name", label: "名称" },
    { prop: "status", label: "状态", type: "tag", dicData: [
      { label: "启用", value: 1, type: "success" },
      { label: "禁用", value: 0, type: "danger" }
    ]}
  ]
});
</script>
<defineOptions>{ name: "demo-example" }</defineOptions>
```

## 7. 常见技巧与注意事项
- 实体类、接口、页面字段命名保持一致，避免前后端不匹配。
- 返回结构统一用 `R.ok()`、`R.error()`，禁止返回Map。
- 注释需覆盖类、字段、方法，风格与官方一致。
- 包路径、命名、注解、继承、接口风格全部合规，便于低代码开发和团队协作。
- 如需自定义操作，参考task、demo模块扩展写法。





