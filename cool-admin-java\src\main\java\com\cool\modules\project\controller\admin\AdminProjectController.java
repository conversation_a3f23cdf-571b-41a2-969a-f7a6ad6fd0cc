package com.cool.modules.project.controller.admin;

import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.core.request.R;
import com.cool.modules.project.entity.ProjectEntity;
import com.cool.modules.project.entity.table.ProjectEntityTableDef;
import com.cool.modules.project.service.ProjectService;

import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;

/**
 * 项目管理
 */
@Tag(name = "项目管理", description = "项目模块统一管理")
@CoolRestController(api = { "add", "delete", "update", "list", "info", "page","importJson" })
public class AdminProjectController extends BaseController<ProjectService, ProjectEntity> {

    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        setPageOption(createOp()
            .fieldEq(
                ProjectEntityTableDef.PROJECT_ENTITY.ID,
                ProjectEntityTableDef.PROJECT_ENTITY.NAME,
                ProjectEntityTableDef.PROJECT_ENTITY.CODE,
                ProjectEntityTableDef.PROJECT_ENTITY.CODE1,
                ProjectEntityTableDef.PROJECT_ENTITY.MANAGER
            ).keyWordLikeFields(
                ProjectEntityTableDef.PROJECT_ENTITY.NAME,
                ProjectEntityTableDef.PROJECT_ENTITY.CODE,
                ProjectEntityTableDef.PROJECT_ENTITY.CODE1,
                ProjectEntityTableDef.PROJECT_ENTITY.MANAGER
            )
        );
        setListOption(createOp().fieldEq(
            ProjectEntityTableDef.PROJECT_ENTITY.ID,
            ProjectEntityTableDef.PROJECT_ENTITY.NAME,
            ProjectEntityTableDef.PROJECT_ENTITY.CODE,
            ProjectEntityTableDef.PROJECT_ENTITY.CODE1,
            ProjectEntityTableDef.PROJECT_ENTITY.MANAGER
        ).keyWordLikeFields(
            ProjectEntityTableDef.PROJECT_ENTITY.NAME,
            ProjectEntityTableDef.PROJECT_ENTITY.CODE,
            ProjectEntityTableDef.PROJECT_ENTITY.CODE1,
            ProjectEntityTableDef.PROJECT_ENTITY.MANAGER
        ));
    }

    /**
     * 批量导入项目（JSON数组）
     */
    @PostMapping("/importJson")
    public R<Integer> importJson(@RequestBody List<ProjectEntity> projects) {
        int count = service.importProjects(projects);
        return R.ok(count);
    }
} 