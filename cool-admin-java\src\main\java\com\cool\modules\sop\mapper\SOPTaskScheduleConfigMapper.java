package com.cool.modules.sop.mapper;

import com.cool.modules.sop.entity.SOPTaskScheduleConfigEntity;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 任务调度配置Mapper
 */
@Mapper
public interface SOPTaskScheduleConfigMapper extends BaseMapper<SOPTaskScheduleConfigEntity> {

    /**
     * 根据配置键查询配置
     */
    SOPTaskScheduleConfigEntity selectByConfigKey(@Param("configKey") String configKey);

    /**
     * 更新配置值
     */
    int updateConfigValue(@Param("configKey") String configKey, @Param("configValue") String configValue);
}
