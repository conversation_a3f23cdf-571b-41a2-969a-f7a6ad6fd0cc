import { type ModuleConfig } from '/@/cool';

export default (): ModuleConfig => {
  return {
    components: [
      () => import('./components/logs.vue')
    ],

    views: [
      {
        path: '/task/list',
        meta: {
          label: '任务调度'
        },
        component: () => import('./views/list.vue')
      },
      {
        path: '/task/info',
        meta: {
          label: '任务管理'
        },
        component: () => import('./views/info.vue')
      },
      {
        path: '/task/package',
        meta: {
          label: '场景任务包'
        },
        component: () => import('./views/package-management.vue')
      }
    ]
  };
};
