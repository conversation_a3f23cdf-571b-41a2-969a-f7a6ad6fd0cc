package com.cool.modules.task.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class TaskExecutionEntityTableDef extends TableDef {

    public static final TaskExecutionEntityTableDef TASK_EXECUTION_ENTITY = new TaskExecutionEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn PHOTOS = new QueryColumn(this, "photos");

    public final QueryColumn REMARK = new QueryColumn(this, "remark");

    public final QueryColumn TASK_ID = new QueryColumn(this, "task_id");

    public final QueryColumn ACCEPT_TIME = new QueryColumn(this, "accept_time");

    public final QueryColumn ASSIGNED_BY = new QueryColumn(this, "assigned_by");

    public final QueryColumn ASSIGNEE_ID = new QueryColumn(this, "assignee_id");

    public final QueryColumn CONFIDENCE = new QueryColumn(this, "confidence");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    public final QueryColumn ATTACHMENTS = new QueryColumn(this, "attachments");

    public final QueryColumn ASSIGNEE_NAME = new QueryColumn(this, "assignee_name");

    /**
     * 执行部门ID
     */
    public final QueryColumn DEPARTMENT_ID = new QueryColumn(this, "department_id");

    public final QueryColumn ASSIGNMENT_TYPE = new QueryColumn(this, "assignment_type");

    public final QueryColumn COMPLETION_NOTE = new QueryColumn(this, "completion_note");

    public final QueryColumn COMPLETION_TIME = new QueryColumn(this, "completion_time");

    public final QueryColumn EXECUTION_STATUS = new QueryColumn(this, "execution_status");

    public final QueryColumn ASSIGNMENT_REASONS = new QueryColumn(this, "assignment_reasons");

    /**
     * 执行人部门ID
     */
    public final QueryColumn ASSIGNEE_DEPARTMENT_ID = new QueryColumn(this, "assignee_department_id");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, PHOTOS, REMARK, TASK_ID, ACCEPT_TIME, ASSIGNED_BY, ASSIGNEE_ID, CONFIDENCE, CREATE_TIME, UPDATE_TIME, ATTACHMENTS, ASSIGNEE_NAME, DEPARTMENT_ID, ASSIGNMENT_TYPE, COMPLETION_NOTE, COMPLETION_TIME, EXECUTION_STATUS, ASSIGNMENT_REASONS, ASSIGNEE_DEPARTMENT_ID};

    public TaskExecutionEntityTableDef() {
        super("", "task_execution");
    }

    private TaskExecutionEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public TaskExecutionEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new TaskExecutionEntityTableDef("", "task_execution", alias));
    }

}
