<template>
  <cl-crud ref="Crud">
    <cl-row>
      <cl-add-btn @click="onCreate" />
      <cl-flex1 />
    </cl-row>
    <cl-table ref="Table">
      <template #slot-op="{ row }">
        <el-button size="small" @click="onReset(row)">重置</el-button>
        <el-button size="small" v-if="row.status === 1" @click="onDisable(row)">禁用</el-button>
        <el-button size="small" v-else @click="onEnable(row)">启用</el-button>
        <el-button size="small" type="danger" @click="onDelete(row)">删除</el-button>
      </template>
    </cl-table>
    <!-- 新建/重置APIKEY弹窗 -->
    <el-dialog v-model="dialogVisible" title="APIKEY明文（只显示一次）" width="500px">
      <div style="word-break: break-all; font-size: 18px; margin-bottom: 16px;">
        <el-alert type="success" :closable="false" show-icon>
          <template #default>
            <span>请妥善保存APIKEY：</span>
            <span style="color: #f56c6c; font-weight: bold;">{{ rawApiKey }}</span>
          </template>
        </el-alert>
      </div>
      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </cl-crud>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useCool } from '/@/cool';
import { useCrud, useTable } from '@cool-vue/crud';

const { service } = useCool();

// 终极兼容：自定义service对象，重写page方法，强制返回{list,pagination}
const Crud = useCrud({
  service: {
    async page(params: any) {
      const res = await service.base.sys.api.key.page(params);
      // 兼容后端返回 { data: { list, pagination } }
      return {
        list: res.list || res.data?.list || [],
        pagination: res.pagination || res.data?.pagination || {}
      };
    },
    async create(data: any) {
      return service.base.sys.api.key.create(data);
    },
    async reset(data: any) {
      return service.base.sys.api.key.reset(data);
    },
    async enable(data: any) {
      return service.base.sys.api.key.enable(data);
    },
    async disable(data: any) {
      return service.base.sys.api.key.disable(data);
    },
    async delete(data: any) {
      return service.base.sys.api.key.delete(data);
    }
  }
});

const Table = useTable({
  columns: [
    { prop: 'apikey', label: 'APIKEY', minWidth: 280 },
    { prop: 'status', label: '状态', minWidth: 80 },
    { prop: 'expireTime', label: '有效期', minWidth: 160 },
    { prop: 'remark', label: '备注', minWidth: 120 },
    { prop: 'createTime', label: '创建时间', minWidth: 160 },
    { type: 'op', label: '操作', width: 260, slot: 'op' }
  ]
});

console.log('[user-apikey] Crud:', Crud);
console.log('[user-apikey] Table:', Table);

onMounted(() => {
  console.log('[user-apikey] component mounted');
  Crud.value?.refresh();
  console.log('[user-apikey] called Crud.value?.refresh()');
});

const dialogVisible = ref(false);
const rawApiKey = ref('');

function onCreate() {
  ElMessageBox.prompt('请输入备注（可选）', '新建APIKEY', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPlaceholder: '备注',
    inputValue: ''
  })
    .then(async ({ value }) => {
      const res = await Crud.value?.service.create({ remark: value });
      rawApiKey.value = res.data?.apikey || res.apikey || '';
      dialogVisible.value = true;
      Crud.value?.refresh();
    })
    .catch(() => {});
}

function onReset(row: any) {
  ElMessageBox.confirm('确定要重置该APIKEY？', '提示', { type: 'warning' })
    .then(async () => {
      const res = await Crud.value?.service.reset({ id: row.id });
      rawApiKey.value = res.data?.apikey || res.apikey || '';
      dialogVisible.value = true;
      Crud.value?.refresh();
    })
    .catch(() => {});
}

function onEnable(row: any) {
  Crud.value?.service.enable({ id: row.id }).then(() => {
    ElMessage.success('已启用');
    Crud.value?.refresh();
  });
}

function onDisable(row: any) {
  Crud.value?.service.disable({ id: row.id }).then(() => {
    ElMessage.success('已禁用');
    Crud.value?.refresh();
  });
}

function onDelete(row: any) {
  ElMessageBox.confirm('确定要删除该APIKEY？', '提示', { type: 'warning' })
    .then(() => {
      Crud.value?.service.delete({ id: row.id }).then(() => {
        ElMessage.success('已删除');
        Crud.value?.refresh();
      });
    })
    .catch(() => {});
}
</script>