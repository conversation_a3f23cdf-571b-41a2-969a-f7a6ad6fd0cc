<template>
  <div class="organization-mode-switcher">
    <!-- 当前模式显示 -->
    <div class="current-mode" @click="showSwitcher = !showSwitcher">
      <el-icon class="mode-icon">
        <component :is="currentModeIcon" />
      </el-icon>
      <span class="mode-text">{{ organizationStore.currentModeName }}</span>
      <el-icon class="arrow-icon" :class="{ 'is-reverse': showSwitcher }">
        <ArrowDown />
      </el-icon>
    </div>

    <!-- 切换器弹出层 -->
    <el-popover
      v-model:visible="showSwitcher"
      placement="bottom-start"
      :width="320"
      trigger="manual"
      popper-class="organization-switcher-popover"
    >
      <template #reference>
        <div></div>
      </template>

      <div class="switcher-content">
        <div class="switcher-header">
          <h4>选择组织形态</h4>
          <p class="description">切换后将重新加载页面以应用新的菜单和权限</p>
        </div>

        <div class="mode-list">
          <div
            v-for="mode in organizationStore.availableModes"
            :key="mode.code"
            class="mode-item"
            :class="{
              'is-current': mode.code === organizationStore.currentMode,
              'is-disabled': !canSwitchToMode(mode.code)
            }"
            @click="handleModeClick(mode)"
          >
            <div class="mode-info">
              <div class="mode-header">
                <el-icon class="mode-icon">
                  <component :is="mode.icon" />
                </el-icon>
                <span class="mode-name">{{ mode.name }}</span>
                <el-tag
                  v-if="mode.code === organizationStore.currentMode"
                  type="success"
                  size="small"
                  effect="plain"
                >
                  当前
                </el-tag>
              </div>
              <p class="mode-description">{{ mode.description }}</p>
            </div>
            
            <el-icon v-if="mode.code === organizationStore.currentMode" class="check-icon">
              <Check />
            </el-icon>
          </div>
        </div>

        <div class="switcher-footer">
          <el-button size="small" @click="showSwitcher = false">取消</el-button>
          <el-button
            type="primary"
            size="small"
            :loading="organizationStore.loading"
            @click="showSwitchDialog = true"
          >
            切换模式
          </el-button>
        </div>
      </div>
    </el-popover>

    <!-- 切换确认对话框 -->
    <el-dialog
      v-model="showSwitchDialog"
      title="确认切换组织形态"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="switch-dialog-content">
        <div class="switch-info">
          <p>
            <strong>当前模式：</strong>{{ organizationStore.currentModeName }}
          </p>
          <p>
            <strong>目标模式：</strong>{{ selectedMode?.name }}
          </p>
        </div>

        <el-form ref="switchFormRef" :model="switchForm" label-width="80px">
          <el-form-item label="切换原因">
            <el-input
              v-model="switchForm.reason"
              type="textarea"
              :rows="3"
              placeholder="请输入切换原因（可选）"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>

        <div class="switch-warning">
          <el-alert
            title="注意"
            type="warning"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>切换组织形态后：</p>
              <ul>
                <li>页面将自动刷新</li>
                <li>菜单和权限将根据新模式重新加载</li>
                <li>当前页面状态将丢失</li>
              </ul>
            </template>
          </el-alert>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showSwitchDialog = false">取消</el-button>
          <el-button
            type="primary"
            :loading="organizationStore.loading"
            @click="confirmSwitch"
          >
            确认切换
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ArrowDown, Check, OfficeBuilding, FolderOpened } from '@element-plus/icons-vue';
import { useOrganizationStore, OrganizationMode } from '../store/organization';

// Store
const organizationStore = useOrganizationStore();

// 响应式数据
const showSwitcher = ref(false);
const showSwitchDialog = ref(false);
const selectedMode = ref<any>(null);

// 切换表单
const switchForm = reactive({
  reason: ''
});

// 计算属性
const currentModeIcon = computed(() => {
  return organizationStore.isDepartmentMode ? OfficeBuilding : FolderOpened;
});

// 方法
const canSwitchToMode = (mode: OrganizationMode) => {
  if (mode === OrganizationMode.DEPARTMENT) {
    return organizationStore.canSwitchToDepartment;
  } else if (mode === OrganizationMode.PROJECT) {
    return organizationStore.canSwitchToProject;
  }
  return false;
};

const handleModeClick = (mode: any) => {
  if (mode.code === organizationStore.currentMode) {
    showSwitcher.value = false;
    return;
  }

  if (!canSwitchToMode(mode.code)) {
    ElMessage.warning('您没有权限切换到此组织形态');
    return;
  }

  selectedMode.value = mode;
  showSwitcher.value = false;
  showSwitchDialog.value = true;
};

const confirmSwitch = async () => {
  if (!selectedMode.value) {
    return;
  }

  try {
    await organizationStore.switchMode({
      targetMode: selectedMode.value.code,
      reason: switchForm.reason
    });

    ElMessage.success('组织形态切换成功');
    showSwitchDialog.value = false;
    
    // 重置表单
    switchForm.reason = '';
    selectedMode.value = null;
    
  } catch (error: any) {
    ElMessage.error(error.message || '切换失败');
  }
};

// 初始化
organizationStore.init();
</script>

<style lang="scss" scoped>
.organization-mode-switcher {
  .current-mode {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
    user-select: none;

    &:hover {
      background-color: var(--el-fill-color-light);
    }

    .mode-icon {
      margin-right: 8px;
      font-size: 16px;
      color: var(--el-color-primary);
    }

    .mode-text {
      font-size: 14px;
      font-weight: 500;
      margin-right: 8px;
    }

    .arrow-icon {
      font-size: 12px;
      transition: transform 0.3s;
      color: var(--el-text-color-secondary);

      &.is-reverse {
        transform: rotate(180deg);
      }
    }
  }
}

.switcher-content {
  .switcher-header {
    margin-bottom: 16px;

    h4 {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
    }

    .description {
      margin: 0;
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }

  .mode-list {
    .mode-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s;
      margin-bottom: 8px;
      border: 1px solid var(--el-border-color-light);

      &:last-child {
        margin-bottom: 0;
      }

      &:hover:not(.is-disabled) {
        background-color: var(--el-fill-color-light);
        border-color: var(--el-color-primary);
      }

      &.is-current {
        background-color: var(--el-color-primary-light-9);
        border-color: var(--el-color-primary);
      }

      &.is-disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .mode-info {
        flex: 1;

        .mode-header {
          display: flex;
          align-items: center;
          margin-bottom: 4px;

          .mode-icon {
            margin-right: 8px;
            font-size: 16px;
            color: var(--el-color-primary);
          }

          .mode-name {
            font-size: 14px;
            font-weight: 500;
            margin-right: 8px;
          }
        }

        .mode-description {
          margin: 0;
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }

      .check-icon {
        font-size: 16px;
        color: var(--el-color-success);
      }
    }
  }

  .switcher-footer {
    margin-top: 16px;
    text-align: right;
  }
}

.switch-dialog-content {
  .switch-info {
    margin-bottom: 20px;
    padding: 12px;
    background-color: var(--el-fill-color-lighter);
    border-radius: 6px;

    p {
      margin: 0 0 8px 0;
      font-size: 14px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .switch-warning {
    margin-top: 20px;

    :deep(.el-alert__content) {
      p {
        margin: 0 0 8px 0;
      }

      ul {
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 4px;
          font-size: 13px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.organization-switcher-popover {
  padding: 16px !important;
}
</style>
