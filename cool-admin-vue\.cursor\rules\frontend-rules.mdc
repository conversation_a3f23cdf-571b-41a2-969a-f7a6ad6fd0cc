---
description: cl-crud 组件示例
globs: *.tsx, *.ts, *.vue
---

# Cool Admin 前端开发规范

## 项目概述
Cool Admin前端是基于Vue 3 + TypeScript + Element Plus的现代化管理系统，采用模块化架构设计，支持组件化开发、状态管理和实时交互。

## 技术栈
- **框架**: Vue 3 + TypeScript + Composition API
- **构建工具**: Vite
- **UI库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Cool框架集成的service
- **包管理**: pnpm
- **CSS预处理器**: SCSS

## 目录结构规范

### 前端项目结构
```
src/
├── App.vue               # 根组件
├── main.ts              # 入口文件
├── config/              # 配置文件
│   ├── dev.ts           # 开发环境配置
│   ├── prod.ts          # 生产环境配置
│   └── index.ts         # 配置入口
├── cool/                # Cool框架核心
│   ├── bootstrap/       # 启动引导
│   ├── hooks/           # 通用hooks
│   ├── module/          # 模块管理
│   ├── router/          # 路由配置
│   ├── service/         # 服务层
│   ├── types/           # 类型定义
│   └── utils/           # 工具函数
├── modules/             # 业务模块
│   ├── base/            # 基础模块
│   ├── task/            # 任务模块
│   ├── sop/             # SOP工单模块
│   └── [module]/        # 其他业务模块
└── plugins/             # 插件系统
    ├── crud/            # CRUD插件
    ├── element-ui/      # Element UI插件
    └── ...              # 其他插件
```

### 业务模块标准结构
```
modules/[module]/
├── views/               # 页面视图
│   ├── index.vue       # 主页面
│   ├── detail.vue      # 详情页面
│   └── [page].vue      # 其他页面
├── components/          # 组件库
│   ├── [Component].vue # 业务组件
│   └── index.ts        # 组件导出
├── dict/                # 数据字典
│   └── index.ts        # 字典定义
├── services/            # 服务层(可选)
├── locales/             # 国际化
├── config.ts            # 模块配置
└── index.ts             # 模块入口
```

## 编码规范

### 1. 组件定义规范
```vue
<template>
  <div class="component-name">
    <!-- 模板内容 -->
  </div>
</template>

<script lang="ts" setup>
// 组件名定义 - 必须
defineOptions({
  name: "module-component-name" // 格式：模块-组件名
});

// 导入
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useCool } from '/@/cool';

// Props定义
interface Props {
  modelValue?: any;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false
});

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: any];
  'change': [value: any];
}>();

// 响应式数据
const loading = ref(false);
const formData = ref({});

// 计算属性
const isDisabled = computed(() => props.disabled || loading.value);

// 方法定义
const handleSubmit = async () => {
  loading.value = true;
  try {
    // 业务逻辑
    emit('change', formData.value);
  } catch (error) {
    ElMessage.error('操作失败');
  } finally {
    loading.value = false;
  }
};

// 生命周期
onMounted(() => {
  // 初始化逻辑
});
</script>

<style lang="scss" scoped>
.component-name {
  // 样式定义
}
</style>
```

### 2. CRUD页面规范
```vue
<template>
  <cl-crud ref="Crud" @load="onLoad">
    <cl-row>
      <cl-refresh-btn />
      <cl-add-btn />
      <cl-multi-delete-btn />
      <cl-adv-btn />
      <cl-flex1 />
      <cl-search ref="Search" />
    </cl-row>

    <cl-adv-search ref="AdvSearch" />

    <cl-row>
      <cl-table ref="Table">
        <!-- 自定义列模板 -->
        <template #column-status="{ scope }">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusLabel(scope.row.status) }}
          </el-tag>
        </template>
      </cl-table>
    </cl-row>

    <cl-row>
      <cl-flex1 />
      <cl-pagination />
    </cl-row>

    <cl-upsert ref="Upsert" />
  </cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
  name: "module-page-name"
});

import { useCrud, useTable, useUpsert, useSearch, useAdvSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";

const { service } = useCool();

// CRUD配置
const Crud = useCrud({
  service: service.module.entity // 使用EPS自动生成的服务
}, (app) => {
  app.refresh();
});

// 表格配置
const Table = useTable({
  columns: [
    { type: "selection", width: 60 },
    { label: "名称", prop: "name", minWidth: 150 },
    { label: "状态", prop: "status", width: 120 },
    { type: "op", buttons: ["edit", "delete"], width: 160 }
  ]
});

// 搜索配置
const Search = useSearch({
  keyPlaceholder: "请输入名称搜索"
});

// 新增编辑配置
const Upsert = useUpsert({
  items: [
    { label: "名称", prop: "name", required: true, component: { name: "el-input" } },
    { label: "状态", prop: "status", component: { name: "el-select", options: statusOptions } }
  ]
});
</script>
```

### 3. 任务管理组件规范

#### 任务流程可视化组件
```vue
<template>
  <div class="task-steps-flow">
    <!-- 任务包信息头部 -->
    <div class="package-header">
      <div class="package-progress">
        <el-progress :percentage="overallProgress" />
      </div>
    </div>

    <!-- 步骤流程图 -->
    <div class="steps-container">
      <div v-for="(row, rowIndex) in stepRows" :key="rowIndex" class="step-row">
        <div 
          v-for="step in row" 
          :key="step.id" 
          class="step-node"
          @click="handleStepClick(step)"
        >
          <div class="step-card">
            <!-- 步骤信息 -->
            <div class="step-header">
              <span class="step-code">{{ step.stepCode }}</span>
              <el-tag :type="getStepStatusType(step)">
                {{ getStepStatusLabel(step) }}
              </el-tag>
            </div>
            
            <!-- 执行人分配 -->
            <div class="assignee-section">
              <div class="assignee-display">
                <span class="role-tag">{{ step.employeeRole }}</span>
                <span class="arrow">→</span>
                <div class="assignee-tags">
                  <el-tag 
                    v-for="assignee in getStepAssignees(step)"
                    :key="assignee.id"
                    size="small"
                  >
                    {{ assignee.name }}
                  </el-tag>
                </div>
              </div>
              <el-button 
                type="primary" 
                text 
                size="small"
                @click="showAssigneeEditor(step)"
              >
                分配/更换
              </el-button>
            </div>

            <!-- 任务列表 -->
            <div class="task-list">
              <div 
                v-for="task in getStepTasks(step.stepCode)" 
                :key="task.id"
                class="task-card"
                :class="getTaskCardClass(task.taskStatus)"
              >
                <div class="task-header">
                  <span class="task-name">{{ task.name }}</span>
                  <el-tag :type="getTaskStatusType(task.taskStatus)" size="small">
                    {{ getTaskStatusLabel(task.taskStatus) }}
                  </el-tag>
                </div>
                
                <div class="task-actions">
                  <el-button 
                    v-if="canCompleteTask(task)"
                    type="success"
                    size="small"
                    @click="handleCompleteTask(task)"
                  >
                    完成任务
                  </el-button>
                  <el-button 
                    v-if="canCloseTask(task)"
                    type="warning"
                    size="small"
                    @click="handleCloseTask(task)"
                  >
                    关闭任务
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useCool } from '/@/cool';

// Props
interface Props {
  packageData: any;
  tasks: any[];
  steps: any[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'refresh': [];
  'task-updated': [task: any];
  'step-updated': [step: any];
}>();

const { service } = useCool();

// 响应式数据
const localTasks = ref([...props.tasks]);
const selectedAssignees = ref<any[]>([]);

// 计算属性
const overallProgress = computed(() => {
  const totalTasks = localTasks.value.length;
  if (totalTasks === 0) return 0;
  const completedTasks = localTasks.value.filter(task => task.taskStatus === 3).length;
  return Math.round((completedTasks / totalTasks) * 100);
});

// 步骤行布局
const stepRows = computed(() => {
  // 将步骤按行排列的逻辑
  const rows = [];
  const stepsPerRow = 4;
  for (let i = 0; i < props.steps.length; i += stepsPerRow) {
    rows.push(props.steps.slice(i, i + stepsPerRow));
  }
  return rows;
});

// 权限判断方法
const canCompleteTask = (task: any) => {
  if (!task) return false;
  
  // 管理员可以完成任何待执行或执行中的任务
  if (isManager()) {
    return [1, 2].includes(task.taskStatus);
  }
  
  // 普通用户只能完成自己被分配的任务
  if (task.executions?.length > 0) {
    const currentUserId = getCurrentUserId();
    return task.executions.some((exec: any) =>
      exec.assigneeId === currentUserId &&
      ['ASSIGNED', 'IN_PROGRESS'].includes(exec.executionStatus)
    );
  }
  
  return false;
};

// 任务操作方法
const handleCompleteTask = async (task: any) => {
  try {
    await ElMessageBox.confirm(
      `确认完成任务"${task.name}"？`,
      '任务完成确认',
      { type: 'warning' }
    );

    await service.request({
      url: '/admin/task/status/task/execution/complete',
      method: 'POST',
      data: {
        taskId: task.id,
        assigneeId: getCurrentUserId(),
        completionNote: '任务完成'
      }
    });

    ElMessage.success('任务完成成功');
    emit('refresh');
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('任务完成失败');
    }
  }
};

// 工具方法
const getCurrentUserId = () => {
  // 从用户状态获取当前用户ID
  return 1; // 实际项目中需要获取真实用户ID
};

const isManager = () => {
  // 检查用户是否是管理员
  return true; // 实际项目中需要检查用户角色
};

// 监听数据变化
watch(() => props.tasks, (newTasks) => {
  localTasks.value = [...newTasks];
}, { deep: true });
</script>

<style lang="scss" scoped>
.task-steps-flow {
  padding: 20px;
  
  .package-header {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
  }
  
  .steps-container {
    .step-row {
      display: flex;
      gap: 16px;
      margin-bottom: 24px;
      
      .step-node {
        flex: 1;
        cursor: pointer;
        
        .step-card {
          padding: 16px;
          border: 1px solid #e4e7ed;
          border-radius: 8px;
          background: white;
          transition: all 0.3s;
          
          &:hover {
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
          }
          
          .step-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            
            .step-code {
              font-weight: bold;
              color: #409eff;
            }
          }
          
          .assignee-section {
            margin-bottom: 12px;
            
            .assignee-display {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;
              
              .arrow {
                color: #909399;
              }
              
              .assignee-tags {
                display: flex;
                gap: 4px;
                flex-wrap: wrap;
              }
            }
          }
          
          .task-list {
            .task-card {
              padding: 8px;
              margin-bottom: 8px;
              border: 1px solid #f0f0f0;
              border-radius: 4px;
              
              &.task-pending-assign {
                border-color: #f56c6c;
                background: #fef0f0;
              }
              
              &.task-pending-execute {
                border-color: #e6a23c;
                background: #fdf6ec;
              }
              
              &.task-executing {
                border-color: #409eff;
                background: #ecf5ff;
              }
              
              &.task-completed {
                border-color: #67c23a;
                background: #f0f9ff;
              }
              
              .task-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                
                .task-name {
                  font-size: 14px;
                  font-weight: 500;
                }
              }
              
              .task-actions {
                display: flex;
                gap: 8px;
                
                .el-button {
                  padding: 4px 8px;
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .task-steps-flow {
    .steps-container {
      .step-row {
        flex-direction: column;
      }
    }
  }
}
</style>
```

### 4. 数据字典规范
```typescript
// dict/index.ts
/**
 * 任务模块数据字典
 */

// 任务类型
export const taskCategoryOptions = [
  { label: '场景步骤', value: 'SOP_STEP', color: 'primary' },
  { label: '日常', value: 'RC', color: 'success' },
  { label: '周期', value: 'ZQ', color: 'warning' },
  { label: '临时', value: 'LS', color: 'info' }
];

// 任务业务状态
export const taskBusinessStatusOptions = [
  { label: '待分配', value: 0, color: 'danger' },
  { label: '待执行', value: 1, color: 'info' },
  { label: '执行中', value: 2, color: 'primary' },
  { label: '已完成', value: 3, color: 'success' },
  { label: '已关闭', value: 4, color: 'warning' }
];

// 获取标签和颜色的工具函数
export const getTaskStatusLabel = (value: number) => {
  const option = taskBusinessStatusOptions.find(item => item.value === value);
  return option ? option.label : '未知';
};

export const getTaskStatusColor = (value: number) => {
  const option = taskBusinessStatusOptions.find(item => item.value === value);
  return option ? option.color : 'info';
};
```

### 5. EPS接口调用规范与技巧

#### 5.1 EPS服务调用基础
```typescript
// 使用EPS自动生成的服务
const { service } = useCool();

// EPS生成的服务路径规则：service.模块.实体类名(小写)
// 例如：SOPIndustryEntity -> service.sop.s.o.p.industry
// 例如：TaskInfoEntity -> service.task.info
// 例如：UserEntity -> service.base.user
```

#### 5.2 标准CRUD操作
```typescript
// 分页查询
const loadData = async () => {
  try {
    const response = await service.task.info.page({
      page: 1,
      size: 20,
      keyword: searchKeyword.value,
      status: filterStatus.value,
      createTime: dateRange.value
    });
    dataList.value = response.list;
    total.value = response.pagination.total;
  } catch (error) {
    ElMessage.error('加载数据失败');
  }
};

// 新增记录
const addRecord = async (formData: any) => {
  try {
    await service.task.info.add(formData);
    ElMessage.success('新增成功');
    refresh();
  } catch (error) {
    ElMessage.error('新增失败');
  }
};

// 更新记录
const updateRecord = async (id: number, formData: any) => {
  try {
    await service.task.info.update({ id, ...formData });
    ElMessage.success('更新成功');
    refresh();
  } catch (error) {
    ElMessage.error('更新失败');
  }
};

// 删除记录
const deleteRecord = async (id: number) => {
  try {
    await service.task.info.delete(id);
    ElMessage.success('删除成功');
    refresh();
  } catch (error) {
    ElMessage.error('删除失败');
  }
};

// 批量删除
const batchDelete = async (ids: number[]) => {
  try {
    await service.task.info.delete(ids);
    ElMessage.success('批量删除成功');
    refresh();
  } catch (error) {
    ElMessage.error('批量删除失败');
  }
};

// 获取详情
const getDetail = async (id: number) => {
  try {
    const response = await service.task.info.info(id);
    return response;
  } catch (error) {
    ElMessage.error('获取详情失败');
    return null;
  }
};
```

#### 5.3 自定义API调用
```typescript
// 方式1：使用service.request直接调用
const completeTask = async (taskId: number) => {
  try {
    await service.request({
      url: '/admin/task/status/task/execution/complete',
      method: 'POST',
      data: { taskId, assigneeId: currentUser.id }
    });
    ElMessage.success('任务完成成功');
  } catch (error) {
    ElMessage.error('任务完成失败');
  }
};

// 方式2：如果Controller继承了BaseController，可以直接调用
const customAction = async (params: any) => {
  try {
    // 假设后端有自定义方法 /admin/task/info/customAction
    const response = await service.task.info.customAction(params);
    return response;
  } catch (error) {
    ElMessage.error('操作失败');
    throw error;
  }
};
```

#### 5.4 EPS服务路径映射规则
```typescript
/**
 * EPS服务路径生成规则：
 * 1. 实体类包路径决定服务路径
 * 2. 实体类名转换为小写
 * 3. 驼峰命名转换为点分隔
 * 
 * 示例映射：
 * com.cool.modules.sop.entity.SOPIndustryEntity
 * -> service.sop.s.o.p.industry
 * 
 * com.cool.modules.task.entity.TaskInfoEntity  
 * -> service.task.info
 * 
 * com.cool.modules.base.entity.sys.UserEntity
 * -> service.base.sys.user
 */

// 常用服务路径示例
const services = {
  // 任务模块
  taskInfo: service.task.info,           // 任务信息
  taskPackage: service.task.package,     // 任务包
  taskExecution: service.task.execution, // 任务执行
  
  // SOP模块  
  sopIndustry: service.sop.s.o.p.industry,     // SOP行业
  sopTemplate: service.sop.s.o.p.template,     // SOP模板
  sopWorkOrder: service.sop.s.o.p.work.order,  // SOP工单
  
  // 基础模块
  user: service.base.sys.user,           // 用户
  role: service.base.sys.role,           // 角色
  menu: service.base.sys.menu,           // 菜单
  dept: service.base.sys.dept,           // 部门
};
```

#### 5.5 分页查询优化技巧
```typescript
// 高级分页查询配置
const loadDataWithAdvancedQuery = async () => {
  try {
    const queryParams = {
      page: currentPage.value,
      size: pageSize.value,
      
      // 关键字搜索（支持多字段）
      keyword: searchKeyword.value,
      
      // 精确匹配条件
      status: filterStatus.value,
      taskCategory: filterCategory.value,
      
      // 范围查询
      createTime: dateRange.value,
      
      // 排序设置
      order: 'createTime',
      sort: 'desc',
      
      // 关联查询（如果后端支持）
      include: ['assignees', 'executions'],
      
      // 字段过滤（只返回需要的字段）
      fields: ['id', 'name', 'status', 'createTime']
    };
    
    const response = await service.task.info.page(queryParams);
    
    // 处理响应数据
    dataList.value = response.list || response.records || [];
    total.value = response.pagination?.total || response.total || 0;
    
  } catch (error) {
    console.error('查询失败:', error);
    ElMessage.error('查询数据失败');
  }
};

// 无限滚动加载
const loadMoreData = async () => {
  if (loading.value || !hasMore.value) return;
  
  loading.value = true;
  try {
    currentPage.value++;
    const response = await service.task.info.page({
      page: currentPage.value,
      size: pageSize.value,
      ...queryParams.value
    });
    
    // 追加数据而不是替换
    dataList.value.push(...(response.list || []));
    hasMore.value = response.list?.length === pageSize.value;
    
  } catch (error) {
    currentPage.value--; // 回退页码
    ElMessage.error('加载更多数据失败');
  } finally {
    loading.value = false;
  }
};
```

#### 5.6 错误处理与重试机制
```typescript
// 带重试机制的API调用
const callApiWithRetry = async (apiCall: () => Promise<any>, maxRetries = 3) => {
  let retries = 0;
  
  while (retries < maxRetries) {
    try {
      return await apiCall();
    } catch (error: any) {
      retries++;
      
      // 判断是否需要重试
      if (retries >= maxRetries || !shouldRetry(error)) {
        throw error;
      }
      
      // 指数退避延迟
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, retries) * 1000));
    }
  }
};

// 判断错误是否需要重试
const shouldRetry = (error: any) => {
  const retryableErrors = [408, 429, 500, 502, 503, 504];
  return retryableErrors.includes(error.response?.status);
};

// 使用示例
const loadDataWithRetry = async () => {
  try {
    const response = await callApiWithRetry(() => 
      service.task.info.page(queryParams.value)
    );
    dataList.value = response.list;
  } catch (error) {
    ElMessage.error('加载数据失败，请重试');
  }
};
```

#### 5.7 请求拦截与响应处理
```typescript
// 全局请求拦截器配置（通常在main.ts或config中配置）
const setupRequestInterceptors = () => {
  // 请求拦截
  service.interceptors.request.use(
    (config) => {
      // 添加loading状态
      if (config.showLoading !== false) {
        showGlobalLoading();
      }
      
      // 添加时间戳防止缓存
      if (config.method === 'get') {
        config.params = {
          ...config.params,
          _t: Date.now()
        };
      }
      
      return config;
    },
    (error) => {
      hideGlobalLoading();
      return Promise.reject(error);
    }
  );
  
  // 响应拦截
  service.interceptors.response.use(
    (response) => {
      hideGlobalLoading();
      
      // 统一处理业务错误码
      if (response.data.code !== 1000) {
        ElMessage.error(response.data.message || '操作失败');
        return Promise.reject(new Error(response.data.message));
      }
      
      return response.data.data;
    },
    (error) => {
      hideGlobalLoading();
      handleApiError(error);
      return Promise.reject(error);
    }
  );
};
```

#### 5.8 批量操作优化
```typescript
// 批量操作队列管理
class BatchOperationQueue {
  private queue: Array<() => Promise<any>> = [];
  private isProcessing = false;
  private concurrency = 3; // 并发数限制
  
  async add(operation: () => Promise<any>) {
    this.queue.push(operation);
    if (!this.isProcessing) {
      this.process();
    }
  }
  
  private async process() {
    this.isProcessing = true;
    
    while (this.queue.length > 0) {
      const batch = this.queue.splice(0, this.concurrency);
      await Promise.allSettled(batch.map(op => op()));
    }
    
    this.isProcessing = false;
  }
}

// 批量更新任务状态
const batchUpdateTaskStatus = async (taskIds: number[], status: number) => {
  const batchQueue = new BatchOperationQueue();
  const results: any[] = [];
  
  for (const taskId of taskIds) {
    batchQueue.add(async () => {
      try {
        const result = await service.request({
          url: '/admin/task/status/update',
          method: 'POST',
          data: { taskId, status }
        });
        results.push({ taskId, success: true, result });
      } catch (error) {
        results.push({ taskId, success: false, error });
      }
    });
  }
  
  // 等待所有操作完成
  await new Promise(resolve => {
    const checkComplete = () => {
      if (results.length === taskIds.length) {
        resolve(results);
      } else {
        setTimeout(checkComplete, 100);
      }
    };
    checkComplete();
  });
  
  return results;
};
```

#### 5.9 缓存策略
```typescript
// API响应缓存管理
class ApiCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  
  set(key: string, data: any, ttl = 5 * 60 * 1000) { // 默认5分钟
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }
  
  get(key: string) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
  
  clear() {
    this.cache.clear();
  }
}

const apiCache = new ApiCache();

// 带缓存的数据加载
const loadDataWithCache = async (cacheKey: string, forceRefresh = false) => {
  if (!forceRefresh) {
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      dataList.value = cachedData.list;
      total.value = cachedData.total;
      return;
    }
  }
  
  try {
    const response = await service.task.info.page(queryParams.value);
    
    // 缓存数据
    apiCache.set(cacheKey, {
      list: response.list,
      total: response.pagination.total
    });
    
    dataList.value = response.list;
    total.value = response.pagination.total;
    
  } catch (error) {
    ElMessage.error('加载数据失败');
  }
};
```

#### 5.10 EPS接口调用最佳实践

##### 必须遵循的规则
1. **服务路径规则**: 严格按照EPS生成的服务路径调用，不要手动创建服务文件
2. **错误处理**: 所有API调用必须包含try-catch错误处理
3. **加载状态**: 长时间操作要显示loading状态
4. **用户反馈**: 操作结果要给用户明确的成功/失败提示

##### 性能优化建议
1. **分页查询**: 合理设置页面大小，避免一次加载过多数据
2. **防抖搜索**: 搜索操作使用防抖，避免频繁请求
3. **缓存策略**: 对不经常变化的数据进行适当缓存
4. **并发控制**: 批量操作时控制并发数量

##### 常见陷阱避免
1. **路径错误**: 确保实体类包路径正确，影响EPS生成的服务路径
2. **参数格式**: 注意日期、数组等特殊类型参数的格式
3. **响应结构**: 不同接口的响应结构可能不同，要适配处理
4. **权限检查**: 调用前检查用户是否有相应操作权限

```typescript
// 完整的API调用示例
const ApiCallExample = {
  // 标准CRUD
  async loadTaskList() {
    const loading = ElLoading.service({ text: '加载中...' });
    try {
      const response = await service.task.info.page({
        page: this.currentPage,
        size: this.pageSize,
        keyword: this.searchKeyword,
        status: this.filterStatus
      });
      
      this.dataList = response.list || [];
      this.total = response.pagination?.total || 0;
      
    } catch (error) {
      console.error('加载任务列表失败:', error);
      ElMessage.error('加载数据失败，请重试');
    } finally {
      loading.close();
    }
  },
  
  // 自定义接口
  async completeTask(taskId: number) {
    try {
      await service.request({
        url: '/admin/task/status/task/execution/complete',
        method: 'POST',
        data: { 
          taskId, 
          assigneeId: this.currentUser.id,
          completionNote: '任务完成'
        }
      });
      
      ElMessage.success('任务完成成功');
      this.refreshTaskList();
      
    } catch (error: any) {
      console.error('完成任务失败:', error);
      const message = error.response?.data?.message || '任务完成失败';
      ElMessage.error(message);
    }
  }
};
```

## 状态管理规范

### 1. 响应式数据管理
```typescript
// 使用ref和reactive
const loading = ref(false);
const formData = reactive({
  name: '',
  status: 1
});

// 计算属性
const isValid = computed(() => {
  return formData.name.length > 0 && formData.status > 0;
});

// 监听数据变化
watch(formData, (newValue) => {
  // 数据变化处理
}, { deep: true });
```

### 2. 组件通信
```typescript
// 父子组件通信
const emit = defineEmits<{
  'update:modelValue': [value: any];
  'change': [value: any];
  'refresh': [];
}>();

// 触发事件
const handleChange = (value: any) => {
  emit('update:modelValue', value);
  emit('change', value);
};

// 跨组件状态共享
const { provide, inject } = Vue;

// 父组件提供状态
provide('currentUser', currentUser);
provide('refreshTasks', loadTasks);

// 子组件注入状态
const currentUser = inject('currentUser');
const refreshTasks = inject('refreshTasks');
```

## 用户体验优化

### 1. 加载状态管理
```typescript
const loading = ref(false);
const buttonLoading = ref<Record<string, boolean>>({});

const handleAction = async (id: string) => {
  buttonLoading.value[id] = true;
  try {
    await performAction(id);
  } finally {
    buttonLoading.value[id] = false;
  }
};
```

### 2. 错误处理
```typescript
const handleError = (error: any, operation: string) => {
  console.error(`${operation}失败:`, error);
  
  if (error.response?.status === 401) {
    ElMessage.error('登录已过期，请重新登录');
    // 跳转到登录页
  } else if (error.response?.status === 403) {
    ElMessage.error('权限不足，无法执行此操作');
  } else {
    ElMessage.error(`${operation}失败: ${error.message || '未知错误'}`);
  }
};
```

### 3. 懒加载优化
```typescript
// Tab切换懒加载
const handleTabChange = async (tabName: string) => {
  const currentData = getDataByTab(tabName);
  
  // 只有当前Tab没有数据时才加载
  if (currentData.length === 0) {
    await loadTabData(tabName);
  }
};

// 虚拟滚动
const virtualListRef = ref();
const itemSize = 80; // 每个项目的高度
```

## 样式规范

### 1. SCSS使用规范
```scss
// 变量定义
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;

// 混入定义
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 组件样式
.component-name {
  padding: 16px;
  
  &__header {
    @include flex-center;
    margin-bottom: 16px;
  }
  
  &__content {
    .item {
      margin-bottom: 8px;
      
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}
```

### 2. 响应式设计
```scss
// 断点定义
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1600px
);

// 响应式混入
@mixin respond-to($breakpoint) {
  @media (min-width: map-get($breakpoints, $breakpoint)) {
    @content;
  }
}

// 使用示例
.component {
  padding: 8px;
  
  @include respond-to(md) {
    padding: 16px;
  }
  
  @include respond-to(lg) {
    padding: 24px;
  }
}
```

## 性能优化

### 1. 组件懒加载
```typescript
// 路由懒加载
const routes = [
  {
    path: '/task/info',
    component: () => import('./views/info.vue')
  }
];

// 组件懒加载
const TaskStepsFlow = defineAsyncComponent(() => import('./components/task-steps-flow.vue'));
```

### 2. 数据缓存
```typescript
// 使用Map缓存数据
const dataCache = new Map<string, any>();

const loadDataWithCache = async (key: string) => {
  if (dataCache.has(key)) {
    return dataCache.get(key);
  }
  
  const data = await service.loadData(key);
  dataCache.set(key, data);
  return data;
};
```

### 3. 防抖节流
```typescript
import { debounce, throttle } from 'lodash-es';

// 搜索防抖
const handleSearch = debounce(async (keyword: string) => {
  await loadData({ keyword });
}, 300);

// 滚动节流
const handleScroll = throttle((event: Event) => {
  // 滚动处理逻辑
}, 100);
```

## 测试规范

### 1. 组件测试
```typescript
import { mount } from '@vue/test-utils';
import { describe, it, expect } from 'vitest';
import TaskCard from '../task-card.vue';

describe('TaskCard', () => {
  it('renders task information correctly', () => {
    const task = {
      id: 1,
      name: '测试任务',
      status: 1
    };
    
    const wrapper = mount(TaskCard, {
      props: { task }
    });
    
    expect(wrapper.text()).toContain('测试任务');
    expect(wrapper.find('.task-status').text()).toBe('待执行');
  });
  
  it('emits complete event when complete button is clicked', async () => {
    const wrapper = mount(TaskCard, {
      props: { task: { id: 1, name: '测试任务', status: 1 } }
    });
    
    await wrapper.find('.complete-btn').trigger('click');
    
    expect(wrapper.emitted('complete')).toBeTruthy();
    expect(wrapper.emitted('complete')[0]).toEqual([1]);
  });
});
```

## 部署配置

### 1. 环境配置
```typescript
// config/dev.ts
export default {
  host: "http://localhost:8001",
  api: "/admin",
  mock: false
};

// config/prod.ts
export default {
  host: "https://api.example.com",
  api: "/admin",
  mock: false
};
```

### 2. 构建优化
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]'
      }
    }
  },
  optimizeDeps: {
    include: ['vue', 'vue-router', 'element-plus']
  }
});
```

## 重要提醒

1. **组件命名**: 必须使用`defineOptions({ name: "模块-页面" })`定义组件名
2. **CRUD组件**: 使用`<cl-table ref="Table" />`而不是`<cl-table ref="Table" v-bind="Table" />`
3. **服务调用**: 严格使用EPS生成的服务路径，不要手动创建服务文件
4. **页面结构**: 复杂页面使用`<el-scrollbar>`包装，参考demo模块的标准写法
5. **开发流程**: 先查看demo模块示例，严格按照demo的代码结构编写

遵循以上规范可以确保代码质量、组件复用性和开发效率。

## 滚动最佳实践

### 问题背景
Cool Admin框架在 `src/modules/base/static/css/index.scss` 中设置了全局样式：
```scss
#app {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}
```
这个设置会阻止整个应用的滚动，导致页面内容超出视口时无法滚动。

### 正确解决方案
**使用 `<el-scrollbar>` 组件包装页面内容**，这是Element Plus提供的滚动容器组件：

```vue
<template>
  <el-scrollbar>
    <div class="page-content">
      <!-- 页面内容 -->
    </div>
  </el-scrollbar>
</template>
```

### 错误做法
❌ **不要**强制覆盖 `#app` 的CSS样式：
```scss
/* 错误做法 - 会破坏框架布局 */
#app {
  overflow: auto !important;
}
```

❌ **不要**使用复杂的CSS选择器覆盖：
```scss
/* 错误做法 - 过于复杂且不稳定 */
body.some-class #app {
  overflow: auto !important;
}
```

### 最佳实践规则
1. **始终使用 `<el-scrollbar>` 包装页面内容**
2. **保持Cool Admin框架的全局CSS不变**
3. **参考 `manager-dashboard.vue` 等标准页面的写法**
4. **不要尝试修改 `#app` 容器的滚动设置**

### 示例代码
```vue
<template>
  <el-scrollbar>
    <div class="my-page">
      <div class="header">
        <!-- 页面头部 -->
      </div>
      
      <div class="content">
        <!-- 页面内容，可以很长 -->
      </div>
      
      <div class="footer">
        <!-- 页面底部 -->
      </div>
    </div>
  </el-scrollbar>
</template>

<script lang="ts" setup>
defineOptions({
  name: "my-page"
});
</script>

<style lang="scss" scoped>
.my-page {
  padding: 24px;
  min-height: 100vh; // 确保有足够的高度
}
</style>
```

### 经验教训
- Cool Admin框架的滚动机制是经过精心设计的，不要轻易修改
- `<el-scrollbar>` 组件专门用于在固定容器内提供滚动功能
- 这种方案保持了左侧菜单和顶部导航的固定，只让内容区域滚动
- 简单、稳定、符合框架设计理念

## SSE实时交互最佳实践

### 后端SSE推送优化
在异步任务处理中，应该遵循以下交互流程：

1. **需求确认阶段**：
   ```java
   updateProgress(recordId, 1, 10, "接收到用户需求：" + request.getTaskDescription());
   ```

2. **AI识别阶段**：
   ```java
   updateProgress(recordId, 1, 20, "AI正在进行综合识别（场景匹配、时间分析、优先级评估）...");
   ```

3. **识别结果展示**：
   ```java
   StringBuilder recognitionResult = new StringBuilder();
   recognitionResult.append("AI识别完成 - ");
   if (aiResult.getScenario() != null) {
       recognitionResult.append("场景：").append(aiResult.getScenario().getScenarioName()).append("；");
   }
   if (aiResult.getPriority() != null) {
       recognitionResult.append("优先级：").append(getPriorityLabel(aiResult.getPriority())).append("；");
   }
   // 添加时间范围信息
   updateProgress(recordId, 1, 40, recognitionResult.toString());
   ```

### 前端进度展示优化
1. **进度条与时间线结合**：同时显示整体进度和详细步骤
2. **消息分类样式**：不同类型的消息使用不同的视觉样式
3. **重要信息高亮**：AI识别结果等关键信息需要特殊标识
4. **动态进度文本**：根据进度百分比显示对应的阶段描述

### 关键实现要点
- 后端需要提供详细的进度信息，包括需求确认、AI识别结果等
- 前端需要根据消息内容进行分类展示，提升用户体验
- AI识别不仅包括场景匹配，还应包括时间分析和优先级评估
- 默认时间范围应该清晰告知用户（如"从今天开始一个月"）

### 前端实现示例
```vue
<template>
  <!-- 进度条 -->
  <div class="progress-bar-container">
    <el-progress
      :percentage="progress"
      :stroke-width="15"
      striped
      striped-flow
      :color="progressColor"
    />
    <div class="progress-text">{{ progressText }}</div>
  </div>

  <!-- 时间线 -->
  <el-timeline>
    <el-timeline-item
      v-for="(item, index) in progressDetails"
      :key="index"
      :timestamp="item.timestamp"
      :type="getTimelineItemType(index)"
      :color="getTimelineItemColor(index)"
    >
      <div class="timeline-content">
        <div class="timeline-message" :class="getTimelineMessageClass(item.message)">
          {{ item.message }}
        </div>
        <div v-if="isRecognitionResult(item.message)" class="recognition-highlight">
          <el-icon><Star /></el-icon>
          <span>AI智能识别结果</span>
        </div>
      </div>
    </el-timeline-item>
  </el-timeline>
</template>

<script lang="ts" setup>
// 进度文本映射
const progressText = computed(() => {
  const progress = currentProgress.value || 0;
  if (progress < 10) return "初始化中...";
  if (progress < 20) return "接收需求中...";
  if (progress < 40) return "AI识别中...";
  if (progress < 60) return "识别完成，准备生成...";
  if (progress < 80) return "任务生成中...";
  return "即将完成...";
});

// 消息分类
const getTimelineMessageClass = (message: string) => {
  if (message.includes("AI识别完成")) return "recognition-message";
  if (message.includes("用户需求")) return "requirement-message";
  if (message.includes("失败")) return "error-message";
  return "normal-message";
};
</script>
```

## 开发规则和最佳实践

### 1. CRUD组件使用规范
- 使用 `useCrud`、`useTable`、`useUpsert` hooks
- 通过 ref 自动注入配置，**不要使用 v-bind 绑定**
- 正确写法：`<cl-table ref="Table" />`
- 错误写法：`<cl-table ref="Table" v-bind="Table" />`

### 2. 组件命名规范
- 必须使用 `defineOptions({ name: "模块-页面" })` 定义组件名
- 组件名格式：`sop-industry`、`sop-template` 等，用于路由缓存
- Table 和 Upsert 首字母大写：`const Table = useTable()`

### 3. 服务调用规范
- 严格使用 EPS 生成的服务路径，如 `service.sop.s.o.p.industry`
- **不要手动创建服务文件**，会与 EPS 系统冲突
- 服务路径由后端实体类包结构自动生成

【重要】RestController接口调用规范：
- 所有自定义 RestController 接口（如 AI 任务生成器、任务状态等）必须统一用 `service.request({ url, method, data })` 调用，不允许直接 import request。
- url 必须为全量 path，method 必须大写，参数放 data 字段。
- 参考任务状态接口写法，保证全局拦截、token、mock、权限、异常处理一致。
- 示例：
```js
await service.request({
  url: '/admin/sop/ai-task-generator/preview',
  method: 'POST',
  data: { ... }
})
```

### 4. 页面结构规范
```vue
<template>
  <cl-crud ref="Crud">
    <cl-row>
      <cl-refresh-btn />
      <cl-add-btn />
      <cl-multi-delete-btn />
      <cl-flex1 />
      <cl-search-key />
    </cl-row>
    
    <cl-row>
      <cl-table ref="Table" />
    </cl-row>
    
    <cl-row>
      <cl-flex1 />
      <cl-pagination />
    </cl-row>
    
    <cl-upsert ref="Upsert" />
  </cl-crud>
</template>
```

### 5. 开发流程
- 先查看 `src/modules/demo` 的对应示例
- 严格按照 demo 的代码结构编写
- 避免使用复杂的 Element Plus 图标导入，优先使用 emoji 或简单图标

【重要】组件复用与单文件长度规范：
- 组件应尽量复用，避免重复造轮子。
- 单文件（.vue/.ts）代码行数建议不超过 500 行，超出需合理拆分为多个子组件或模块。
- 拆分时优先按功能、UI块、业务逻辑分层，保持每个文件职责单一、易维护。
- 拆分后的子组件/模块应有清晰命名和注释，便于团队协作和复用。
- 复用组件应放在 modules/xxx/components 或 plugins/xxx/components 目录，避免散落。
- 参考 demo、base、task 等模块的组件拆分和复用方式。
