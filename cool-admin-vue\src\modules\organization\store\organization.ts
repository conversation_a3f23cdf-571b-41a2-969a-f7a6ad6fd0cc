import { defineStore } from 'pinia';
import { service } from '/@/cool';

/**
 * 组织形态枚举
 */
export enum OrganizationMode {
  DEPARTMENT = 'DEPARTMENT',
  PROJECT = 'PROJECT'
}

/**
 * 组织状态接口
 */
export interface OrganizationState {
  currentMode: OrganizationMode;
  canSwitchToDepartment: boolean;
  canSwitchToProject: boolean;
  permissionScope: any;
  switchHistory: any;
  loading: boolean;
}

/**
 * 组织形态切换参数
 */
export interface SwitchModeParams {
  targetMode: OrganizationMode;
  reason?: string;
  forceSwitch?: boolean;
}

/**
 * 组织状态管理Store
 */
export const useOrganizationStore = defineStore('organization', {
  state: (): OrganizationState => ({
    currentMode: OrganizationMode.DEPARTMENT,
    canSwitchToDepartment: true,
    canSwitchToProject: false,
    permissionScope: null,
    switchHistory: null,
    loading: false
  }),

  getters: {
    /**
     * 是否为部门模式
     */
    isDepartmentMode: (state) => state.currentMode === OrganizationMode.DEPARTMENT,

    /**
     * 是否为项目模式
     */
    isProjectMode: (state) => state.currentMode === OrganizationMode.PROJECT,

    /**
     * 当前模式显示名称
     */
    currentModeName: (state) => {
      return state.currentMode === OrganizationMode.DEPARTMENT ? '部门维度' : '项目维度';
    },

    /**
     * 是否可以切换模式
     */
    canSwitchMode: (state) => {
      return state.canSwitchToDepartment || state.canSwitchToProject;
    },

    /**
     * 可切换的模式列表
     */
    availableModes: (state) => {
      const modes = [];
      if (state.canSwitchToDepartment) {
        modes.push({
          code: OrganizationMode.DEPARTMENT,
          name: '部门维度',
          description: '基于部门层级的组织管理模式',
          icon: 'el-icon-office-building'
        });
      }
      if (state.canSwitchToProject) {
        modes.push({
          code: OrganizationMode.PROJECT,
          name: '项目维度',
          description: '基于项目的组织管理模式',
          icon: 'el-icon-folder-opened'
        });
      }
      return modes;
    }
  },

  actions: {
    /**
     * 初始化组织信息
     */
    async init() {
      try {
        this.loading = true;
        await this.loadOrganizationInfo();
        await this.loadPermissionScope();
      } catch (error) {
        console.error('初始化组织信息失败:', error);
      } finally {
        this.loading = false;
      }
    },

    /**
     * 加载组织信息
     */
    async loadOrganizationInfo() {
      try {
        const result = await service.request({
          url: '/admin/organization/mode/current',
          method: 'GET'
        });

        if (result.code === 1000) {
          this.currentMode = result.data.currentMode;
          this.canSwitchToDepartment = result.data.canSwitchToDepartment;
          this.canSwitchToProject = result.data.canSwitchToProject;
        }
      } catch (error) {
        console.error('加载组织信息失败:', error);
        throw error;
      }
    },

    /**
     * 加载权限范围
     */
    async loadPermissionScope() {
      try {
        const result = await service.request({
          url: '/admin/organization/mode/permission-scope',
          method: 'GET'
        });

        if (result.code === 1000) {
          this.permissionScope = result.data;
        }
      } catch (error) {
        console.error('加载权限范围失败:', error);
      }
    },

    /**
     * 切换组织模式
     */
    async switchMode(params: SwitchModeParams) {
      try {
        this.loading = true;

        const result = await service.request({
          url: '/admin/organization/mode/switch',
          method: 'POST',
          data: {
            targetMode: params.targetMode,
            reason: params.reason,
            forceSwitch: params.forceSwitch || false
          }
        });

        if (result.code === 1000) {
          // 更新当前模式
          this.currentMode = params.targetMode;

          // 重新加载组织信息和权限
          await this.loadOrganizationInfo();
          await this.loadPermissionScope();

          // 刷新菜单和权限
          await this.refreshMenuAndPermissions();

          // 刷新页面以加载新的菜单和路由
          window.location.reload();

          return true;
        } else {
          throw new Error(result.message || '切换失败');
        }
      } catch (error) {
        console.error('切换组织模式失败:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 刷新菜单和权限
     */
    async refreshMenuAndPermissions() {
      try {
        // 重新获取菜单数据
        const { menu } = useBase();
        await menu.get();

        // 刷新权限缓存
        await service.request({
          url: '/admin/organization/mode/refresh-cache',
          method: 'POST'
        });
      } catch (error) {
        console.error('刷新菜单和权限失败:', error);
      }
    },

    /**
     * 验证模式是否可切换
     */
    async validateMode(mode: OrganizationMode) {
      try {
        const result = await service.request({
          url: `/admin/organization/mode/validate-mode/${mode}`,
          method: 'GET'
        });

        return result.code === 1000 ? result.data : null;
      } catch (error) {
        console.error('验证模式失败:', error);
        return null;
      }
    },

    /**
     * 获取切换历史
     */
    async loadSwitchHistory() {
      try {
        const result = await service.request({
          url: '/admin/organization/mode/switch-history',
          method: 'GET'
        });

        if (result.code === 1000) {
          this.switchHistory = result.data;
        }
      } catch (error) {
        console.error('加载切换历史失败:', error);
      }
    },

    /**
     * 重置状态
     */
    reset() {
      this.currentMode = OrganizationMode.DEPARTMENT;
      this.canSwitchToDepartment = true;
      this.canSwitchToProject = false;
      this.permissionScope = null;
      this.switchHistory = null;
      this.loading = false;
    }
  },

  persist: {
    key: 'cool-organization-store',
    storage: localStorage,
    paths: ['currentMode']
  }
});

/**
 * 在组件外使用
 */
export function useOrganization() {
  return useOrganizationStore();
}
