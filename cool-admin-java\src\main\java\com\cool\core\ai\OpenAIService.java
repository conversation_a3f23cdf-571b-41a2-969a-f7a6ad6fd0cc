package com.cool.core.ai;

import com.cool.core.config.AIProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * OpenAI API服务
 * 提供对OpenAI API的封装调用
 */
@Slf4j
@Service
public class OpenAIService {

    @Autowired
    private AIProperties aiProperties;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @PostConstruct
    public void init() {
        log.info("OpenAI服务初始化...");
        if (aiProperties != null) {
            log.info("AI配置已加载: enabled={}", aiProperties.isEnabled());
            if (aiProperties.getOpenai() != null) {
                log.info("OpenAI配置: baseUrl={}, model={}",
                    aiProperties.getOpenai().getBaseUrl(),
                    aiProperties.getOpenai().getModel());
            } else {
                log.warn("OpenAI配置为空");
            }
        } else {
            log.error("AI配置未加载");
        }
    }

    /**
     * 调用OpenAI Chat Completion API
     */
    public ChatCompletionResponse chatCompletion(ChatCompletionRequest request) {
        try {
            log.info("调用OpenAI API，模型: {}, 消息数: {}", request.getModel(), request.getMessages().size());

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + aiProperties.getOpenai().getApiKey());
            headers.set("User-Agent", "Cool-Admin-Java/1.0");

            // 构建请求体
            HttpEntity<ChatCompletionRequest> entity = new HttpEntity<>(request, headers);

            // 发送请求
            String url = aiProperties.getOpenai().getBaseUrl() + "/chat/completions";
            ResponseEntity<ChatCompletionResponse> response = restTemplate.exchange(
                    url, HttpMethod.POST, entity, ChatCompletionResponse.class
            );

            ChatCompletionResponse result = response.getBody();
            if (result == null) {
                throw new RuntimeException("OpenAI API返回空响应");
            }

            log.info("OpenAI API调用成功，使用tokens: {}", result.getUsage().getTotalTokens());
            return result;

        } catch (Exception e) {
            log.error("OpenAI API调用失败", e);
            throw new RuntimeException("OpenAI API调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 简化的聊天接口
     */
    public String chat(String systemPrompt, String userMessage) {
        List<ChatMessage> messages = new ArrayList<>();
        
        if (systemPrompt != null && !systemPrompt.trim().isEmpty()) {
            messages.add(new ChatMessage("system", systemPrompt));
        }
        
        messages.add(new ChatMessage("user", userMessage));

        ChatCompletionRequest request = ChatCompletionRequest.builder()
                .model(aiProperties.getOpenai().getModel())
                .messages(messages)
                .temperature(aiProperties.getOpenai().getTemperature() != null ? aiProperties.getOpenai().getTemperature() : 0.1)
                .maxTokens(aiProperties.getOpenai().getMaxTokens() != null ? aiProperties.getOpenai().getMaxTokens() : 4000)
                .build();

        ChatCompletionResponse response = chatCompletion(request);
        
        if (response.getChoices() != null && !response.getChoices().isEmpty()) {
            return response.getChoices().get(0).getMessage().getContent();
        }
        
        throw new RuntimeException("OpenAI API未返回有效响应");
    }

    /**
     * JSON格式响应的聊天接口
     */
    public Map<String, Object> chatForJson(String systemPrompt, String userMessage) {
        String jsonResponse = chat(systemPrompt + "\n\n请以JSON格式返回结果，不要使用markdown代码块包裹。", userMessage);

        try {
            // 清理响应文本，移除可能的markdown代码块
            String cleanedJson = cleanJsonResponse(jsonResponse);

            // 尝试解析JSON
            @SuppressWarnings("unchecked")
            Map<String, Object> result = objectMapper.readValue(cleanedJson, Map.class);
            return result;
        } catch (Exception e) {
            log.warn("解析JSON响应失败，原始文本: {}", jsonResponse);

            // 尝试从原始文本中提取JSON
            String extractedJson = extractJsonFromText(jsonResponse);
            if (extractedJson != null) {
                try {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> result = objectMapper.readValue(extractedJson, Map.class);
                    log.info("成功从文本中提取并解析JSON");
                    return result;
                } catch (Exception ex) {
                    log.warn("提取的JSON仍然无法解析: {}", extractedJson);
                }
            }

            // 如果解析失败，返回包含原始文本的Map
            Map<String, Object> result = new HashMap<>();
            result.put("text", jsonResponse);
            result.put("success", false);
            result.put("error", "JSON解析失败");
            return result;
        }
    }

    /**
     * 清理JSON响应，移除markdown代码块等格式
     */
    public String cleanJsonResponse(String response) {
        if (response == null) return null;

        String cleaned = response.trim();

        // 移除markdown代码块
        if (cleaned.startsWith("```json")) {
            cleaned = cleaned.substring(7);
        } else if (cleaned.startsWith("```")) {
            cleaned = cleaned.substring(3);
        }

        if (cleaned.endsWith("```")) {
            cleaned = cleaned.substring(0, cleaned.length() - 3);
        }

        return cleaned.trim();
    }

    /**
     * 从文本中提取JSON内容
     */
    private String extractJsonFromText(String text) {
        if (text == null) return null;

        // 尝试找到JSON对象的开始和结束
        int start = text.indexOf('{');
        int end = text.lastIndexOf('}');

        if (start >= 0 && end > start) {
            return text.substring(start, end + 1);
        }

        return null;
    }

    // DTO类定义
    @Data
    public static class ChatCompletionRequest {
        private String model;
        private List<ChatMessage> messages;
        private Double temperature;
        @JsonProperty("max_tokens")
        private Integer maxTokens;
        @JsonProperty("top_p")
        private Double topP;
        @JsonProperty("frequency_penalty")
        private Double frequencyPenalty;
        @JsonProperty("presence_penalty")
        private Double presencePenalty;

        public static ChatCompletionRequestBuilder builder() {
            return new ChatCompletionRequestBuilder();
        }

        public static class ChatCompletionRequestBuilder {
            private ChatCompletionRequest request = new ChatCompletionRequest();

            public ChatCompletionRequestBuilder model(String model) {
                request.setModel(model);
                return this;
            }

            public ChatCompletionRequestBuilder messages(List<ChatMessage> messages) {
                request.setMessages(messages);
                return this;
            }

            public ChatCompletionRequestBuilder temperature(Double temperature) {
                request.setTemperature(temperature);
                return this;
            }

            public ChatCompletionRequestBuilder maxTokens(Integer maxTokens) {
                request.setMaxTokens(maxTokens);
                return this;
            }

            public ChatCompletionRequest build() {
                return request;
            }
        }
    }

    @Data
    public static class ChatMessage {
        private String role;
        private String content;

        public ChatMessage() {}

        public ChatMessage(String role, String content) {
            this.role = role;
            this.content = content;
        }
    }

    @Data
    public static class ChatCompletionResponse {
        private String id;
        private String object;
        private Long created;
        private String model;
        private List<Choice> choices;
        private Usage usage;
        @JsonProperty("system_fingerprint")
        private String systemFingerprint;
    }

    @Data
    public static class Choice {
        private Integer index;
        private ChatMessage message;
        @JsonProperty("finish_reason")
        private String finishReason;
    }

    @Data
    public static class Usage {
        @JsonProperty("prompt_tokens")
        private Integer promptTokens;
        @JsonProperty("completion_tokens")
        private Integer completionTokens;
        @JsonProperty("total_tokens")
        private Integer totalTokens;
    }
}
