---
alwaysApply: true
---

# 开发总结
- 开发总结的md文档放到cool-admin-java\doc\DEV目录下
- 同一个功能模块的要给我合并文件，文件不要太碎

# Cool Admin 后端开发规范

## 项目概述
Cool Admin是一个基于Spring Boot 3的现代化管理系统后端，采用模块化架构设计，支持AI编码、多租户、流程编排等特性。

## 技术栈
- **框架**: Spring Boot 3.2.5, Spring Security, Spring Data
- **数据库**: MyBatis-Flex, MySQL/PostgreSQL, Redis
- **构建工具**: Maven 3.8+
- **JDK版本**: Java 17+
- **任务调度**: Spring Quartz
- **缓存**: Caffeine/Redis
- **文档**: SpringDoc OpenAPI 3
- **代码生成**: AutoTable

## 目录结构规范

### 核心模块结构
```
src/main/java/com/cool/
├── core/                    # 核心框架
│   ├── annotation/         # 注解定义
│   ├── base/              # 基础类(BaseEntity, BaseService等)
│   ├── cache/             # 缓存组件
│   ├── config/            # 配置类
│   ├── exception/         # 异常处理
│   ├── interceptor/       # 拦截器
│   ├── mybatis/           # MyBatis扩展
│   └── util/              # 工具类
├── modules/               # 业务模块
│   ├── base/             # 基础模块(用户、角色、菜单等)
│   ├── sop/              # SOP工单模块
│   ├── user/             # 用户模块
│   ├── task/             # 任务模块
│   └── [module]/         # 其他业务模块
└── CoolApplication.java   # 启动类
```

### 业务模块标准结构
```
modules/[module]/
├── controller/           # 控制器层
├── service/             # 服务层
│   └── impl/           # 服务实现
├── entity/             # 实体类
├── mapper/             # Mapper接口
├── dto/                # 数据传输对象
├── enums/              # 枚举类
└── config.ts           # 模块配置(前端)
```

## 编码规范

### 1. 实体类规范
```java
@Table(value = "模块_表名", comment = "表注释")
public class ExampleEntity extends BaseEntity {
    
    @Id
    @KeyType(KeyType.Auto)
    @ColumnDefine(comment = "主键ID")
    private Long id;
    
    @ColumnDefine(comment = "字段注释", length = 100)
    private String fieldName;
    
    @ColumnDefine(comment = "状态", type = "tinyint", defaultValue = "1")
    private Integer status;
    
    @ColumnDefine(comment = "创建时间")
    private Date createTime;
}
```

### 2. 服务层规范
```java
@Service
public class ExampleServiceImpl extends BaseServiceImpl<ExampleMapper, ExampleEntity> implements ExampleService {
    
    @Override
    @Transactional
    public void customMethod(Long id) {
        // 业务逻辑实现
        ExampleEntity entity = getById(id);
        if (entity == null) {
            throw new CoolPreconditions.check("记录不存在");
        }
        // 更新操作
        updateById(entity);
    }
}
```

### 3. 控制器规范
```java
@RestController
@RequestMapping("/admin/example")
@Tag(name = "示例管理", description = "示例模块相关接口")
public class AdminExampleController extends BaseController<ExampleService, ExampleEntity> {
    
    @PostMapping("/custom")
    @Operation(summary = "自定义操作")
    public R customAction(@RequestBody @Valid CustomRequest request) {
        // 调用服务层方法
        exampleService.customMethod(request.getId());
        return R.ok();
    }
}
```

### 4. DTO规范
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExampleRequest {
    
    @NotNull(message = "ID不能为空")
    private Long id;
    
    @NotBlank(message = "名称不能为空")
    @Length(max = 100, message = "名称长度不能超过100字符")
    private String name;
}
```

## 任务系统设计模式

### 1. 多层次任务架构
```
任务系统架构
├── 任务包层 (TaskPackage) - 场景级任务容器
├── 任务层 (Task) - 具体执行单元  
├── 执行层 (TaskExecution) - 执行人分配与状态管理
└── 历史层 (TaskHistory) - 操作历史与审计跟踪
```

### 2. 状态流转管理
```java
// 任务业务状态枚举
public enum TaskBusinessStatusEnum {
    PENDING_ASSIGN(0, "待分配"),
    PENDING_EXECUTE(1, "待执行"),
    EXECUTING(2, "执行中"),
    COMPLETED(3, "已完成"),
    CLOSED(4, "已关闭");
}

// 执行状态枚举
public enum ExecutionStatusEnum {
    ASSIGNED("ASSIGNED", "已分配"),
    IN_PROGRESS("IN_PROGRESS", "执行中"),
    COMPLETED("COMPLETED", "已完成"),
    CANCELLED("CANCELLED", "已取消"),
    REJECTED("REJECTED", "已拒绝");
}
```

### 3. 状态管理服务
```java
@Service
@RequiredArgsConstructor
public class TaskStatusServiceImpl implements TaskStatusService {
    
    private final TaskExecutionService taskExecutionService;
    private final TaskInfoService taskInfoService;
    private final TaskHistoryService taskHistoryService;
    private final TaskPermissionService taskPermissionService;
    
    @Override
    @Transactional
    public Boolean completeTaskExecution(TaskCompletionRequest request) {
        // 1. 权限检查
        if (!taskPermissionService.canCompleteTaskExecution(request.getTaskId(), request.getAssigneeId())) {
            return false;
        }
        
        // 2. 更新执行记录
        TaskExecutionEntity execution = taskExecutionService.getByTaskIdAndAssigneeId(
            request.getTaskId(), request.getAssigneeId());
        execution.setExecutionStatus("COMPLETED");
        execution.setCompletionTime(new Date());
        taskExecutionService.updateById(execution);
        
        // 3. 检查是否所有执行人都完成
        if (areAllExecutionsCompleted(request.getTaskId())) {
            updateTaskStatus(request.getTaskId(), TaskBusinessStatusEnum.COMPLETED.getCode());
        }
        
        // 4. 记录操作历史
        taskHistoryService.recordTaskHistory(
            request.getTaskId(), "COMPLETE", oldStatus, newStatus, 
            request.getAssigneeId(), execution.getAssigneeName(), "完成任务执行");
            
        return true;
    }
}
```

### 4. 历史记录与审计
```java
@Service
public class TaskHistoryServiceImpl implements TaskHistoryService {
    
    @Override
    public void recordTaskHistory(Long taskId, String actionType, Integer oldStatus, Integer newStatus,
                                 Long actionBy, String actionByName, String note) {
        TaskHistoryEntity history = new TaskHistoryEntity();
        history.setTaskId(taskId);
        history.setActionType(actionType);
        history.setOldStatus(oldStatus);
        history.setNewStatus(newStatus);
        history.setActionBy(actionBy);
        history.setActionByName(actionByName);
        history.setActionTime(new Date());
        history.setNote(note);
        save(history);
    }
}
```

## AI增强功能开发

### 1. AI驱动的智能SOP工单系统架构

#### 核心AI能力模块
```java
/**
 * SOP智能解析服务
 */
@Service
public class AISOPParserService {
    
    /**
     * 自然语言SOP解析
     */
    public SOPParseResult parseNaturalLanguageSOP(String description, String industry);
    
    /**
     * SOP模板智能生成
     */
    public SOPTemplate generateSOPTemplate(SOPGenerateRequest request);
    
    /**
     * SOP知识库检索
     */
    public List<SOPKnowledge> searchSOPKnowledge(String keyword, String industry);
}

/**
 * 智能调度引擎
 */
@Service
public class IntelligentScheduleEngineService {
    
    /**
     * 智能任务调度
     */
    public ScheduleDecision intelligentSchedule(ScheduleContext context);
    
    /**
     * 动态调度优化
     */
    public ScheduleOptimization optimizeSchedule(Long workOrderId);
}
```

### 2. AI配置规范
```yaml
cool:
  ai:
    # 大语言模型配置
    llm:
      openai:
        api-key: ${OPENAI_API_KEY:your-api-key}
        base-url: ${OPENAI_BASE_URL:https://api.openai.com/v1}
        model: ${OPENAI_MODEL:gpt-4}
        timeout: 30000
    
    # AI功能开关
    features:
      sop-generation: true
      intelligent-scheduling: true
      execution-guidance: true
      quality-check: true
      process-optimization: true
```

## 开发最佳实践

### 1. 异常处理
- 使用`CoolPreconditions.check(condition, message)`进行参数校验
- 业务异常抛出`CoolException`
- 统一异常处理通过`GlobalExceptionHandler`

### 2. 事务管理
- 服务层方法添加`@Transactional`注解
- 只读操作使用`@Transactional(readOnly = true)`
- 复杂业务逻辑考虑事务传播机制

### 3. 缓存使用
```java
@Cacheable(value = "example", key = "#id")
public ExampleEntity getById(Long id) {
    return super.getById(id);
}

@CacheEvict(value = "example", key = "#entity.id")
public void updateById(ExampleEntity entity) {
    super.updateById(entity);
}
```

### 4. 数据库查询
```java
// 使用QueryWrapper构建查询条件
QueryWrapper wrapper = QueryWrapper.create()
    .eq("status", 1)
    .like("name", keyword)
    .between("create_time", startTime, endTime)
    .orderBy("create_time", false);
```

### 5. 日志规范
```java
@Slf4j
public class ExampleService {
    
    public void method() {
        log.info("执行业务操作，参数: {}", param);
        log.debug("调试信息: {}", debugInfo);
        log.error("操作失败", exception);
    }
}
```

### 6. 注释规范（Service方法与核心业务逻辑）
- 所有 Service 层方法必须添加方法注释，说明方法用途、参数、返回值、异常等。
- 复杂业务流、关键分支、易混淆的处理逻辑，必须有详细的行内注释，便于团队理解和后续维护。
- 关键业务流、AI能力调用、状态流转、分布式事务等核心代码，注释要覆盖设计意图和注意事项。
- 注释风格建议：
```java
/**
 * 生成AI任务并推送进度
 * @param request 任务生成请求参数
 * @return 多部门任务生成响应
 * @throws CoolException AI能力调用失败时抛出
 */
public MultiDepartmentGenerateResponse generateTasksByAI(TaskGenerateRequest request) {
    // 1. 参数校验
    // 2. 记录任务生成请求
    // 3. 调用AI能力服务
    // 4. 组装任务包/任务/执行人
    // 5. 推送SSE进度
    // 6. 落库
}
```
- 禁止出现无注释、注释与代码不符、无意义注释（如"// TODO"）等情况。

## 数据库设计规范

### 1. 表命名
- 使用模块前缀：`模块名_表名`
- 小写字母，下划线分隔
- 例如：`task_info`, `task_execution`, `task_history`

### 2. 字段规范
- 主键：`id` (bigint, auto_increment)
- 创建时间：`create_time` (datetime)
- 更新时间：`update_time` (datetime)
- 创建人：`create_by` (bigint)
- 更新人：`update_by` (bigint)
- 状态字段：`status` (tinyint, 1-启用 0-禁用)

### 3. 索引规范
- 主键索引：`PRIMARY KEY`
- 唯一索引：`uk_字段名`
- 普通索引：`idx_字段名`
- 组合索引：`idx_字段1_字段2`

## 安全规范

### 1. 接口权限
- 管理端接口：`/admin/**` 需要登录验证
- 应用端接口：`/app/**` 需要token验证
- 开放接口：`/open/**` 无需验证

### 2. 数据权限
- 多租户数据隔离
- 角色权限控制
- 数据范围权限

### 3. 参数校验
- 使用Bean Validation注解
- 自定义校验器
- SQL注入防护

## 性能优化

### 1. 查询优化
- 避免N+1查询
- 合理使用分页
- 索引优化

### 2. 缓存策略
- 热点数据缓存
- 查询结果缓存
- 分布式缓存

### 3. 异步处理
- 耗时操作异步执行
- 消息队列处理
- 线程池配置

## 测试规范

### 1. 单元测试
```java
@SpringBootTest
class ExampleServiceTest {
    
    @Autowired
    private ExampleService exampleService;
    
    @Test
    void testCustomMethod() {
        // 测试逻辑
    }
}
```

### 2. 集成测试
- 数据库集成测试
- API接口测试
- 业务流程测试

## 部署配置

### 1. 环境配置
- `application-local.yml` - 本地开发
- `application-dev.yml` - 开发环境
- `application-prod.yml` - 生产环境

### 2. Docker部署
```dockerfile
FROM openjdk:17-jre-slim
COPY target/cool-admin.jar app.jar
EXPOSE 8001
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 代码审查清单

### 1. 代码质量
- [ ] 遵循命名规范
- [ ] 遵守设计规范，不要循环依赖
- [ ] 异常处理完整
- [ ] 日志记录合理
- [ ] 注释清晰完整

### 2. 安全检查
- [ ] 参数校验完整
- [ ] SQL注入防护
- [ ] 权限控制正确
- [ ] 敏感数据保护

### 3. 性能考虑
- [ ] 查询效率优化
- [ ] 缓存使用合理
- [ ] 事务范围适当
- [ ] 资源释放及时

## 常用工具类

### 1. 工具类使用
- `CoolUtil` - 通用工具
- `DateUtil` - 日期处理
- `JsonUtil` - JSON操作
- `StringUtil` - 字符串处理
- `ValidateUtil` - 数据校验

### 2. 响应格式
```java
// 成功响应
return R.ok(data);

// 失败响应  
return R.error("错误信息");

// 分页响应
return R.ok(pageResult);
```
禁止使用Map包装返回，必须要使用实体类


## 重要提醒

1. **状态更新规则**: 所有任务状态更新必须通过`/admin/task/status`接口进行，确保数据同步和统计正确
2. **实体类路径**: 实体类必须放在正确的包路径下，影响EPS生成的服务路径
3. **Controller规范**: 继承BaseController，确保所有CRUD接口正确实现
4. **EPS系统**: 不要手动修改EPS生成的文件，实体类变更后需要重新生成EPS

遵循以上规范可以确保代码质量、可维护性和团队协作效率。 

use mcp_server_mysql操作数据

## Controller/Service 职责与边界

### 1. Controller 层职责
- 只负责接收请求、参数校验、权限校验、调用 Service 层、组装/返回响应。
- 不允许写业务逻辑、数据处理、AI能力调用细节。
- 路径、参数、响应结构需与前端约定一致。
- 统一继承 BaseController，所有 CRUD 接口（page, add, update, delete, info）必须实现。
- AI相关接口（如任务生成、预览、进度、历史）仅负责转发到对应 Service，不做任何业务流处理。

### 2. Service 层职责
- 只负责业务逻辑实现、数据处理、AI能力调用、事务控制。
- 业务流（如任务生成、进度、SSE推送、历史记录）必须在 Service 层实现，Controller 不得介入。
- Service 之间调用需避免循环依赖，AI能力与业务流分离：
  - AI 能力（如场景识别、对话、质量检查等）全部由 AILLMService 及其实现类负责，**不包含任何业务流**。
  - 业务流（如任务生成、进度、SSE推送、工单/任务包/任务落地、历史记录等）全部由 AiTaskGenerateRecordService 及其实现类负责，**不直接调用 AI 业务流**。
- 任务状态、进度、日志、SSE推送、历史记录等，必须通过统一的 Service 方法管理，禁止 Controller 直接操作数据库或推送。

### 3. AI能力与业务流分离最佳实践
- AILLMService 只定义和实现 AI 能力型方法（如 performUnifiedAIRecognition、chat、suggestScenarios、qualityCheck、selectScenario 等），不涉及任何业务流、数据落地、SSE推送。
- AiTaskGenerateRecordService 只负责 AI 任务生成、进度、SSE推送、历史记录、工单/任务包/任务落地等完整业务流，所有进度、日志、结果必须落库并推送。
- Controller 层所有 AI 任务相关接口（如 /ai-task-generator/preview、/generate、/history、/stream 等）统一调用 AiTaskGenerateRecordService，不再调用 AILLMService 的业务流方法。
- 只有纯 AI 能力型接口（如场景建议、AI对话、质量检查等）才允许 Controller 直接调用 AILLMService。

### 4. 依赖注入与调用边界
- Controller 只注入本模块 Service（如 AiTaskGenerateRecordService、AILLMService），不跨模块注入。
- Service 层如需调用 AI 能力，统一通过注入 AILLMService 实现。
- 禁止 Service 之间循环依赖，业务流与 AI 能力分层清晰。

### 5. 典型示例
```java
// Controller 层
@RestController
@RequestMapping("/admin/sop/ai-task-generator")
public class AiTaskGeneratorController {
    @Autowired
    private AiTaskGenerateRecordService aiTaskGenerateRecordService;
    @Autowired
    private AILLMService aiLLMService; // 仅用于AI能力型接口

    @PostMapping("/preview")
    public R preview(@RequestBody TaskGenerateRequest req) {
        return R.ok(aiTaskGenerateRecordService.generatePreviewTasksByAI(req));
    }
    // ... 其它接口 ...
}

// Service 层
@Service
public class AiTaskGenerateRecordServiceImpl implements AiTaskGenerateRecordService {
    @Autowired
    private AILLMService aillmService;
    // 只在此处调用 AI 能力
    public MultiDepartmentGenerateResponse generatePreviewTasksByAI(TaskGenerateRequest req) {
        // 1. 记录任务、2. 调用 aillmService.performUnifiedAIRecognition、3. 组装预览、4. 推送进度、5. 落库
    }
}

@Service
public class AILLMServiceImpl implements AILLMService {
    public AIRecognitionResult performUnifiedAIRecognition(String desc, String context) {
        // 只做AI能力调用，不落库、不推送、不做业务流
    }
}
```
### 6. AdminTaskDepartmentPermissionController / TaskDepartmentPermissionService
- **Controller职责**：负责任务部门权限相关接口（权限检查、批量检查、授权部门/执行人、创建/分配权限、权限概览），参数校验、调用TaskDepartmentPermissionService。
- **Service职责**：实现任务包/任务/执行的部门权限校验、批量权限、授权部门/执行人获取、权限过滤、创建/分配权限等业务逻辑。
- **边界说明**：Controller不处理权限判定细节，所有权限相关操作均通过TaskDepartmentPermissionService实现。

## 通用用户查询接口规范

### 1. 接口位置
- **通用查询接口**：`/admin/base/user-query/*` - 由用户模块提供的通用查询接口
- **业务模块接口**：各业务模块如需用户查询功能，应调用用户模块的通用接口，避免重复实现

### 2. 核心接口
```java
// 1. 通用条件查询接口
POST /admin/base/user-query/list
Body: UserQueryRequest

// 2. 获取可用执行人接口
GET /admin/base/user-query/available-assignees?departmentIds=1,2&roleIds=3,4&name=张&phone=138

// 3. 按部门查询用户
GET /admin/base/user-query/by-department/{departmentId}

// 4. 按角色查询用户  
GET /admin/base/user-query/by-role/{roleId}
```

### 3. UserQueryRequest DTO规范
```java
@Data
@Schema(description = "用户查询请求")
public class UserQueryRequest {
    // 筛选条件
    private List<Long> departmentIds;        // 部门ID列表
    private List<Long> roleIds;              // 角色ID列表
    private List<String> roleNames;          // 角色名称列表
    private String name;                     // 姓名模糊查询
    private String phone;                    // 手机号模糊查询
    private String username;                 // 用户名模糊查询
    private String email;                    // 邮箱模糊查询
    private Integer status;                  // 用户状态
    
    // 控制参数
    private Boolean excludeAdmin = true;     // 是否排除admin用户
    private Boolean includeRoles = true;     // 是否包含角色信息
    private Boolean includeDepartment = true; // 是否包含部门信息
    
    // 排序参数
    private String orderBy;                  // 排序字段
    private String orderDirection = "asc";   // 排序方向
}
```

### 4. 数据权限控制
- **admin用户**：可以查看所有用户数据
- **普通用户**：只能查看有权限的部门用户数据
- **权限获取**：通过 `coolCache.get("admin:department:" + userId, Long[].class)` 获取用户部门权限
- **权限过滤**：在查询条件中自动添加部门权限过滤

### 5. 业务模块调用规范
```java
// 业务模块Controller示例
@RestController
@RequestMapping("/admin/sop/ai-task-generator")
public class AiTaskGeneratorController {
    
    private final BaseSysUserService baseSysUserService;
    
    @GetMapping("/available-assignees")
    public R getAvailableAssignees(
            @RequestParam(required = false) List<Long> departmentIds,
            @RequestParam(required = false) List<Long> roleIds,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String phone) {
        
        // 构建查询请求
        UserQueryRequest request = new UserQueryRequest();
        request.setDepartmentIds(departmentIds);
        request.setRoleIds(roleIds);
        request.setName(name);
        request.setPhone(phone);
        request.setStatus(1);
        request.setExcludeAdmin(true);
        request.setIncludeRoles(true);
        request.setIncludeDepartment(true);
        
        // 调用用户模块通用查询方法
        List<BaseSysUserEntity> users = baseSysUserService.queryUsers(request);
        
        // 只补充业务相关字段
        for (BaseSysUserEntity user : users) {
            user.setWorkload(getWorkload(user.getId()));
            user.setPerformanceScore(getPerformanceScore(user.getId()));
        }
        
        return R.ok(users);
    }
}
```

### 6. 禁止的做法
- ❌ 在业务模块中重复实现用户查询逻辑
- ❌ 使用 Map 包装用户数据返回
- ❌ 在 for 循环中查询数据库获取用户信息
- ❌ 忽略数据权限控制
- ❌ 不支持筛选条件的固定查询

### 7. 推荐的做法
- ✅ 使用用户模块提供的通用查询接口
- ✅ 返回 BaseSysUserEntity 实体类
- ✅ 支持数据权限控制
- ✅ 支持多种筛选条件
- ✅ 一次性联表查询避免N+1问题
- ✅ 业务模块只补充业务相关字段

### 8. 扩展说明
如需新增用户查询场景，应：
1. 优先在 UserQueryRequest 中添加新的筛选条件
2. 在 BaseSysUserServiceImpl.queryUsers() 中添加对应的查询逻辑
3. 在 AdminBaseSysUserQueryController 中添加便捷接口（可选）# 开发总结
- 开发总结的md文档放到cool-admin-java\doc\DEV目录下
- 同一个功能模块的要给我合并文件，文件不要太碎

# Cool Admin 后端开发规范

## 项目概述
Cool Admin是一个基于Spring Boot 3的现代化管理系统后端，采用模块化架构设计，支持AI编码、多租户、流程编排等特性。

## 技术栈
- **框架**: Spring Boot 3.2.5, Spring Security, Spring Data
- **数据库**: MyBatis-Flex, MySQL/PostgreSQL, Redis
- **构建工具**: Maven 3.8+
- **JDK版本**: Java 17+
- **任务调度**: Spring Quartz
- **缓存**: Caffeine/Redis
- **文档**: SpringDoc OpenAPI 3
- **代码生成**: AutoTable

## 目录结构规范

### 核心模块结构
```
src/main/java/com/cool/
├── core/                    # 核心框架
│   ├── annotation/         # 注解定义
│   ├── base/              # 基础类(BaseEntity, BaseService等)
│   ├── cache/             # 缓存组件
│   ├── config/            # 配置类
│   ├── exception/         # 异常处理
│   ├── interceptor/       # 拦截器
│   ├── mybatis/           # MyBatis扩展
│   └── util/              # 工具类
├── modules/               # 业务模块
│   ├── base/             # 基础模块(用户、角色、菜单等)
│   ├── sop/              # SOP工单模块
│   ├── user/             # 用户模块
│   ├── task/             # 任务模块
│   └── [module]/         # 其他业务模块
└── CoolApplication.java   # 启动类
```

### 业务模块标准结构
```
modules/[module]/
├── controller/           # 控制器层
├── service/             # 服务层
│   └── impl/           # 服务实现
├── entity/             # 实体类
├── mapper/             # Mapper接口
├── dto/                # 数据传输对象
├── enums/              # 枚举类
└── config.ts           # 模块配置(前端)
```

## 编码规范

### 1. 实体类规范
```java
@Table(value = "模块_表名", comment = "表注释")
public class ExampleEntity extends BaseEntity {
    
    @Id
    @KeyType(KeyType.Auto)
    @ColumnDefine(comment = "主键ID")
    private Long id;
    
    @ColumnDefine(comment = "字段注释", length = 100)
    private String fieldName;
    
    @ColumnDefine(comment = "状态", type = "tinyint", defaultValue = "1")
    private Integer status;
    
    @ColumnDefine(comment = "创建时间")
    private Date createTime;
}
```

### 2. 服务层规范
```java
@Service
public class ExampleServiceImpl extends BaseServiceImpl<ExampleMapper, ExampleEntity> implements ExampleService {
    
    @Override
    @Transactional
    public void customMethod(Long id) {
        // 业务逻辑实现
        ExampleEntity entity = getById(id);
        if (entity == null) {
            throw new CoolPreconditions.check("记录不存在");
        }
        // 更新操作
        updateById(entity);
    }
}
```

### 3. 控制器规范
```java
@RestController
@RequestMapping("/admin/example")
@Tag(name = "示例管理", description = "示例模块相关接口")
public class AdminExampleController extends BaseController<ExampleService, ExampleEntity> {
    
    @PostMapping("/custom")
    @Operation(summary = "自定义操作")
    public R customAction(@RequestBody @Valid CustomRequest request) {
        // 调用服务层方法
        exampleService.customMethod(request.getId());
        return R.ok();
    }
}
```

### 4. DTO规范
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExampleRequest {
    
    @NotNull(message = "ID不能为空")
    private Long id;
    
    @NotBlank(message = "名称不能为空")
    @Length(max = 100, message = "名称长度不能超过100字符")
    private String name;
}
```

## 任务系统设计模式

### 1. 多层次任务架构
```
任务系统架构
├── 任务包层 (TaskPackage) - 场景级任务容器
├── 任务层 (Task) - 具体执行单元  
├── 执行层 (TaskExecution) - 执行人分配与状态管理
└── 历史层 (TaskHistory) - 操作历史与审计跟踪
```

### 2. 状态流转管理
```java
// 任务业务状态枚举
public enum TaskBusinessStatusEnum {
    PENDING_ASSIGN(0, "待分配"),
    PENDING_EXECUTE(1, "待执行"),
    EXECUTING(2, "执行中"),
    COMPLETED(3, "已完成"),
    CLOSED(4, "已关闭");
}

// 执行状态枚举
public enum ExecutionStatusEnum {
    ASSIGNED("ASSIGNED", "已分配"),
    IN_PROGRESS("IN_PROGRESS", "执行中"),
    COMPLETED("COMPLETED", "已完成"),
    CANCELLED("CANCELLED", "已取消"),
    REJECTED("REJECTED", "已拒绝");
}
```

### 3. 状态管理服务
```java
@Service
@RequiredArgsConstructor
public class TaskStatusServiceImpl implements TaskStatusService {
    
    private final TaskExecutionService taskExecutionService;
    private final TaskInfoService taskInfoService;
    private final TaskHistoryService taskHistoryService;
    private final TaskPermissionService taskPermissionService;
    
    @Override
    @Transactional
    public Boolean completeTaskExecution(TaskCompletionRequest request) {
        // 1. 权限检查
        if (!taskPermissionService.canCompleteTaskExecution(request.getTaskId(), request.getAssigneeId())) {
            return false;
        }
        
        // 2. 更新执行记录
        TaskExecutionEntity execution = taskExecutionService.getByTaskIdAndAssigneeId(
            request.getTaskId(), request.getAssigneeId());
        execution.setExecutionStatus("COMPLETED");
        execution.setCompletionTime(new Date());
        taskExecutionService.updateById(execution);
        
        // 3. 检查是否所有执行人都完成
        if (areAllExecutionsCompleted(request.getTaskId())) {
            updateTaskStatus(request.getTaskId(), TaskBusinessStatusEnum.COMPLETED.getCode());
        }
        
        // 4. 记录操作历史
        taskHistoryService.recordTaskHistory(
            request.getTaskId(), "COMPLETE", oldStatus, newStatus, 
            request.getAssigneeId(), execution.getAssigneeName(), "完成任务执行");
            
        return true;
    }
}
```

### 4. 历史记录与审计
```java
@Service
public class TaskHistoryServiceImpl implements TaskHistoryService {
    
    @Override
    public void recordTaskHistory(Long taskId, String actionType, Integer oldStatus, Integer newStatus,
                                 Long actionBy, String actionByName, String note) {
        TaskHistoryEntity history = new TaskHistoryEntity();
        history.setTaskId(taskId);
        history.setActionType(actionType);
        history.setOldStatus(oldStatus);
        history.setNewStatus(newStatus);
        history.setActionBy(actionBy);
        history.setActionByName(actionByName);
        history.setActionTime(new Date());
        history.setNote(note);
        save(history);
    }
}
```

## AI增强功能开发

### 1. AI驱动的智能SOP工单系统架构

#### 核心AI能力模块
```java
/**
 * SOP智能解析服务
 */
@Service
public class AISOPParserService {
    
    /**
     * 自然语言SOP解析
     */
    public SOPParseResult parseNaturalLanguageSOP(String description, String industry);
    
    /**
     * SOP模板智能生成
     */
    public SOPTemplate generateSOPTemplate(SOPGenerateRequest request);
    
    /**
     * SOP知识库检索
     */
    public List<SOPKnowledge> searchSOPKnowledge(String keyword, String industry);
}

/**
 * 智能调度引擎
 */
@Service
public class IntelligentScheduleEngineService {
    
    /**
     * 智能任务调度
     */
    public ScheduleDecision intelligentSchedule(ScheduleContext context);
    
    /**
     * 动态调度优化
     */
    public ScheduleOptimization optimizeSchedule(Long workOrderId);
}
```

### 2. AI配置规范
```yaml
cool:
  ai:
    # 大语言模型配置
    llm:
      openai:
        api-key: ${OPENAI_API_KEY:your-api-key}
        base-url: ${OPENAI_BASE_URL:https://api.openai.com/v1}
        model: ${OPENAI_MODEL:gpt-4}
        timeout: 30000
    
    # AI功能开关
    features:
      sop-generation: true
      intelligent-scheduling: true
      execution-guidance: true
      quality-check: true
      process-optimization: true
```

## 开发最佳实践

### 1. 异常处理
- 使用`CoolPreconditions.check(condition, message)`进行参数校验
- 业务异常抛出`CoolException`
- 统一异常处理通过`GlobalExceptionHandler`

### 2. 事务管理
- 服务层方法添加`@Transactional`注解
- 只读操作使用`@Transactional(readOnly = true)`
- 复杂业务逻辑考虑事务传播机制

### 3. 缓存使用
```java
@Cacheable(value = "example", key = "#id")
public ExampleEntity getById(Long id) {
    return super.getById(id);
}

@CacheEvict(value = "example", key = "#entity.id")
public void updateById(ExampleEntity entity) {
    super.updateById(entity);
}
```

### 4. 数据库查询
```java
// 使用QueryWrapper构建查询条件
QueryWrapper wrapper = QueryWrapper.create()
    .eq("status", 1)
    .like("name", keyword)
    .between("create_time", startTime, endTime)
    .orderBy("create_time", false);
```

### 5. 日志规范
```java
@Slf4j
public class ExampleService {
    
    public void method() {
        log.info("执行业务操作，参数: {}", param);
        log.debug("调试信息: {}", debugInfo);
        log.error("操作失败", exception);
    }
}
```

### 6. 注释规范（Service方法与核心业务逻辑）
- 所有 Service 层方法必须添加方法注释，说明方法用途、参数、返回值、异常等。
- 复杂业务流、关键分支、易混淆的处理逻辑，必须有详细的行内注释，便于团队理解和后续维护。
- 关键业务流、AI能力调用、状态流转、分布式事务等核心代码，注释要覆盖设计意图和注意事项。
- 注释风格建议：
```java
/**
 * 生成AI任务并推送进度
 * @param request 任务生成请求参数
 * @return 多部门任务生成响应
 * @throws CoolException AI能力调用失败时抛出
 */
public MultiDepartmentGenerateResponse generateTasksByAI(TaskGenerateRequest request) {
    // 1. 参数校验
    // 2. 记录任务生成请求
    // 3. 调用AI能力服务
    // 4. 组装任务包/任务/执行人
    // 5. 推送SSE进度
    // 6. 落库
}
```
- 禁止出现无注释、注释与代码不符、无意义注释（如"// TODO"）等情况。

## 数据库设计规范

### 1. 表命名
- 使用模块前缀：`模块名_表名`
- 小写字母，下划线分隔
- 例如：`task_info`, `task_execution`, `task_history`

### 2. 字段规范
- 主键：`id` (bigint, auto_increment)
- 创建时间：`create_time` (datetime)
- 更新时间：`update_time` (datetime)
- 创建人：`create_by` (bigint)
- 更新人：`update_by` (bigint)
- 状态字段：`status` (tinyint, 1-启用 0-禁用)

### 3. 索引规范
- 主键索引：`PRIMARY KEY`
- 唯一索引：`uk_字段名`
- 普通索引：`idx_字段名`
- 组合索引：`idx_字段1_字段2`

## 安全规范

### 1. 接口权限
- 管理端接口：`/admin/**` 需要登录验证
- 应用端接口：`/app/**` 需要token验证
- 开放接口：`/open/**` 无需验证

### 2. 数据权限
- 多租户数据隔离
- 角色权限控制
- 数据范围权限

### 3. 参数校验
- 使用Bean Validation注解
- 自定义校验器
- SQL注入防护

## 性能优化

### 1. 查询优化
- 避免N+1查询
- 合理使用分页
- 索引优化

### 2. 缓存策略
- 热点数据缓存
- 查询结果缓存
- 分布式缓存

### 3. 异步处理
- 耗时操作异步执行
- 消息队列处理
- 线程池配置

## 测试规范

### 1. 单元测试
```java
@SpringBootTest
class ExampleServiceTest {
    
    @Autowired
    private ExampleService exampleService;
    
    @Test
    void testCustomMethod() {
        // 测试逻辑
    }
}
```

### 2. 集成测试
- 数据库集成测试
- API接口测试
- 业务流程测试

## 部署配置

### 1. 环境配置
- `application-local.yml` - 本地开发
- `application-dev.yml` - 开发环境
- `application-prod.yml` - 生产环境

### 2. Docker部署
```dockerfile
FROM openjdk:17-jre-slim
COPY target/cool-admin.jar app.jar
EXPOSE 8001
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 代码审查清单

### 1. 代码质量
- [ ] 遵循命名规范
- [ ] 遵守设计规范，不要循环依赖
- [ ] 异常处理完整
- [ ] 日志记录合理
- [ ] 注释清晰完整

### 2. 安全检查
- [ ] 参数校验完整
- [ ] SQL注入防护
- [ ] 权限控制正确
- [ ] 敏感数据保护

### 3. 性能考虑
- [ ] 查询效率优化
- [ ] 缓存使用合理
- [ ] 事务范围适当
- [ ] 资源释放及时

## 常用工具类

### 1. 工具类使用
- `CoolUtil` - 通用工具
- `DateUtil` - 日期处理
- `JsonUtil` - JSON操作
- `StringUtil` - 字符串处理
- `ValidateUtil` - 数据校验

### 2. 响应格式
```java
// 成功响应
return R.ok(data);

// 失败响应  
return R.error("错误信息");

// 分页响应
return R.ok(pageResult);
```
禁止使用Map包装返回，必须要使用实体类


## 重要提醒

1. **状态更新规则**: 所有任务状态更新必须通过`/admin/task/status`接口进行，确保数据同步和统计正确
2. **实体类路径**: 实体类必须放在正确的包路径下，影响EPS生成的服务路径
3. **Controller规范**: 继承BaseController，确保所有CRUD接口正确实现
4. **EPS系统**: 不要手动修改EPS生成的文件，实体类变更后需要重新生成EPS

遵循以上规范可以确保代码质量、可维护性和团队协作效率。 

use mcp_server_mysql操作数据

## Controller/Service 职责与边界

### 1. Controller 层职责
- 只负责接收请求、参数校验、权限校验、调用 Service 层、组装/返回响应。
- 不允许写业务逻辑、数据处理、AI能力调用细节。
- 路径、参数、响应结构需与前端约定一致。
- 统一继承 BaseController，所有 CRUD 接口（page, add, update, delete, info）必须实现。
- AI相关接口（如任务生成、预览、进度、历史）仅负责转发到对应 Service，不做任何业务流处理。

### 2. Service 层职责
- 只负责业务逻辑实现、数据处理、AI能力调用、事务控制。
- 业务流（如任务生成、进度、SSE推送、历史记录）必须在 Service 层实现，Controller 不得介入。
- Service 之间调用需避免循环依赖，AI能力与业务流分离：
  - AI 能力（如场景识别、对话、质量检查等）全部由 AILLMService 及其实现类负责，**不包含任何业务流**。
  - 业务流（如任务生成、进度、SSE推送、工单/任务包/任务落地、历史记录等）全部由 AiTaskGenerateRecordService 及其实现类负责，**不直接调用 AI 业务流**。
- 任务状态、进度、日志、SSE推送、历史记录等，必须通过统一的 Service 方法管理，禁止 Controller 直接操作数据库或推送。

### 3. AI能力与业务流分离最佳实践
- AILLMService 只定义和实现 AI 能力型方法（如 performUnifiedAIRecognition、chat、suggestScenarios、qualityCheck、selectScenario 等），不涉及任何业务流、数据落地、SSE推送。
- AiTaskGenerateRecordService 只负责 AI 任务生成、进度、SSE推送、历史记录、工单/任务包/任务落地等完整业务流，所有进度、日志、结果必须落库并推送。
- Controller 层所有 AI 任务相关接口（如 /ai-task-generator/preview、/generate、/history、/stream 等）统一调用 AiTaskGenerateRecordService，不再调用 AILLMService 的业务流方法。
- 只有纯 AI 能力型接口（如场景建议、AI对话、质量检查等）才允许 Controller 直接调用 AILLMService。

### 4. 依赖注入与调用边界
- Controller 只注入本模块 Service（如 AiTaskGenerateRecordService、AILLMService），不跨模块注入。
- Service 层如需调用 AI 能力，统一通过注入 AILLMService 实现。
- 禁止 Service 之间循环依赖，业务流与 AI 能力分层清晰。

### 5. 典型示例
```java
// Controller 层
@RestController
@RequestMapping("/admin/sop/ai-task-generator")
public class AiTaskGeneratorController {
    @Autowired
    private AiTaskGenerateRecordService aiTaskGenerateRecordService;
    @Autowired
    private AILLMService aiLLMService; // 仅用于AI能力型接口

    @PostMapping("/preview")
    public R preview(@RequestBody TaskGenerateRequest req) {
        return R.ok(aiTaskGenerateRecordService.generatePreviewTasksByAI(req));
    }
    // ... 其它接口 ...
}

// Service 层
@Service
public class AiTaskGenerateRecordServiceImpl implements AiTaskGenerateRecordService {
    @Autowired
    private AILLMService aillmService;
    // 只在此处调用 AI 能力
    public MultiDepartmentGenerateResponse generatePreviewTasksByAI(TaskGenerateRequest req) {
        // 1. 记录任务、2. 调用 aillmService.performUnifiedAIRecognition、3. 组装预览、4. 推送进度、5. 落库
    }
}

@Service
public class AILLMServiceImpl implements AILLMService {
    public AIRecognitionResult performUnifiedAIRecognition(String desc, String context) {
        // 只做AI能力调用，不落库、不推送、不做业务流
    }
}
```
### 6. AdminTaskDepartmentPermissionController / TaskDepartmentPermissionService
- **Controller职责**：负责任务部门权限相关接口（权限检查、批量检查、授权部门/执行人、创建/分配权限、权限概览），参数校验、调用TaskDepartmentPermissionService。
- **Service职责**：实现任务包/任务/执行的部门权限校验、批量权限、授权部门/执行人获取、权限过滤、创建/分配权限等业务逻辑。
- **边界说明**：Controller不处理权限判定细节，所有权限相关操作均通过TaskDepartmentPermissionService实现。

## 通用用户查询接口规范

### 1. 接口位置
- **通用查询接口**：`/admin/base/user-query/*` - 由用户模块提供的通用查询接口
- **业务模块接口**：各业务模块如需用户查询功能，应调用用户模块的通用接口，避免重复实现

### 2. 核心接口
```java
// 1. 通用条件查询接口
POST /admin/base/user-query/list
Body: UserQueryRequest

// 2. 获取可用执行人接口
GET /admin/base/user-query/available-assignees?departmentIds=1,2&roleIds=3,4&name=张&phone=138

// 3. 按部门查询用户
GET /admin/base/user-query/by-department/{departmentId}

// 4. 按角色查询用户  
GET /admin/base/user-query/by-role/{roleId}
```

### 3. UserQueryRequest DTO规范
```java
@Data
@Schema(description = "用户查询请求")
public class UserQueryRequest {
    // 筛选条件
    private List<Long> departmentIds;        // 部门ID列表
    private List<Long> roleIds;              // 角色ID列表
    private List<String> roleNames;          // 角色名称列表
    private String name;                     // 姓名模糊查询
    private String phone;                    // 手机号模糊查询
    private String username;                 // 用户名模糊查询
    private String email;                    // 邮箱模糊查询
    private Integer status;                  // 用户状态
    
    // 控制参数
    private Boolean excludeAdmin = true;     // 是否排除admin用户
    private Boolean includeRoles = true;     // 是否包含角色信息
    private Boolean includeDepartment = true; // 是否包含部门信息
    
    // 排序参数
    private String orderBy;                  // 排序字段
    private String orderDirection = "asc";   // 排序方向
}
```

### 4. 数据权限控制
- **admin用户**：可以查看所有用户数据
- **普通用户**：只能查看有权限的部门用户数据
- **权限获取**：通过 `coolCache.get("admin:department:" + userId, Long[].class)` 获取用户部门权限
- **权限过滤**：在查询条件中自动添加部门权限过滤

### 5. 业务模块调用规范
```java
// 业务模块Controller示例
@RestController
@RequestMapping("/admin/sop/ai-task-generator")
public class AiTaskGeneratorController {
    
    private final BaseSysUserService baseSysUserService;
    
    @GetMapping("/available-assignees")
    public R getAvailableAssignees(
            @RequestParam(required = false) List<Long> departmentIds,
            @RequestParam(required = false) List<Long> roleIds,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String phone) {
        
        // 构建查询请求
        UserQueryRequest request = new UserQueryRequest();
        request.setDepartmentIds(departmentIds);
        request.setRoleIds(roleIds);
        request.setName(name);
        request.setPhone(phone);
        request.setStatus(1);
        request.setExcludeAdmin(true);
        request.setIncludeRoles(true);
        request.setIncludeDepartment(true);
        
        // 调用用户模块通用查询方法
        List<BaseSysUserEntity> users = baseSysUserService.queryUsers(request);
        
        // 只补充业务相关字段
        for (BaseSysUserEntity user : users) {
            user.setWorkload(getWorkload(user.getId()));
            user.setPerformanceScore(getPerformanceScore(user.getId()));
        }
        
        return R.ok(users);
    }
}
```

### 6. 禁止的做法
- ❌ 在业务模块中重复实现用户查询逻辑
- ❌ 使用 Map 包装用户数据返回
- 禁止使用Map传参
- ❌ 在 for 循环中查询数据库获取用户信息
- ❌ 忽略数据权限控制
- ❌ 不支持筛选条件的固定查询

### 7. 推荐的做法
- ✅ 使用用户模块提供的通用查询接口
- ✅ 返回 BaseSysUserEntity 实体类
- ✅ 支持数据权限控制
- ✅ 支持多种筛选条件
- ✅ 一次性联表查询避免N+1问题
- ✅ 业务模块只补充业务相关字段

### 8. 扩展说明
如需新增用户查询场景，应：
1. 优先在 UserQueryRequest 中添加新的筛选条件
2. 在 BaseSysUserServiceImpl.queryUsers() 中添加对应的查询逻辑
3. 在 AdminBaseSysUserQueryController 中添加便捷接口（可选）