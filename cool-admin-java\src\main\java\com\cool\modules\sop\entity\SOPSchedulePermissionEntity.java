package com.cool.modules.sop.entity;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;


/**
 * 调度权限配置实体
 */
@Getter
@Setter
@Table(value = "sop_schedule_permission", comment = "调度权限配置表")
@Schema(description = "调度权限配置")
public class SOPSchedulePermissionEntity extends BaseEntity<SOPSchedulePermissionEntity> {

    @ColumnDefine(comment = "角色ID", type = "bigint", notNull = true)
    @Schema(description = "角色ID")
    private Long roleId;

    @ColumnDefine(comment = "权限类型", length = 20, notNull = true)
    @Schema(description = "权限类型：ASSIGN/ADJUST/VIEW")
    private String permissionType;

    @ColumnDefine(comment = "是否启用", defaultValue = "1")
    @Schema(description = "是否启用")
    private Boolean isEnabled;
}
