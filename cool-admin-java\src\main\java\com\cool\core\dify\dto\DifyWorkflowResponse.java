package com.cool.core.dify.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/**
 * Dify工作流响应DTO
 */

public class DifyWorkflowResponse {
    
    /**
     * 工作流运行ID
     */
    @JsonProperty("workflow_run_id")
    private String workflowRunId;
    
    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    private String taskId;
    
    /**
     * 输出数据
     */
    private Map<String, Object> data;
    
    /**
     * 错误信息
     */
    private String error;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 已用时间(秒)
     */
    @JsonProperty("elapsed_time")
    private Double elapsedTime;
    
    /**
     * 总Token数
     */
    @JsonProperty("total_tokens")
    private Integer totalTokens;
    
    /**
     * 创建时间
     */
    @JsonProperty("created_at")
    private Long createdAt;
    
    /**
     * 完成时间
     */
    @JsonProperty("finished_at")
    private Long finishedAt;
    
    /**
     * 文件列表
     */
    private Object[] files;
    
    /**
     * 元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return error == null && "succeeded".equals(status);
    }
    
    /**
     * 检查是否失败
     */
    public boolean isFailed() {
        return error != null || "failed".equals(status);
    }
    
    /**
     * 检查是否运行中
     */
    public boolean isRunning() {
        return "running".equals(status);
    }

    // Getter和Setter方法
    public String getWorkflowRunId() { return workflowRunId; }
    public void setWorkflowRunId(String workflowRunId) { this.workflowRunId = workflowRunId; }

    public String getTaskId() { return taskId; }
    public void setTaskId(String taskId) { this.taskId = taskId; }

    public Map<String, Object> getData() { return data; }
    public void setData(Map<String, Object> data) { this.data = data; }

    public String getError() { return error; }
    public void setError(String error) { this.error = error; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public Double getElapsedTime() { return elapsedTime; }
    public void setElapsedTime(Double elapsedTime) { this.elapsedTime = elapsedTime; }

    public Integer getTotalTokens() { return totalTokens; }
    public void setTotalTokens(Integer totalTokens) { this.totalTokens = totalTokens; }

    public Long getCreatedAt() { return createdAt; }
    public void setCreatedAt(Long createdAt) { this.createdAt = createdAt; }

    public Long getFinishedAt() { return finishedAt; }
    public void setFinishedAt(Long finishedAt) { this.finishedAt = finishedAt; }

    public Object[] getFiles() { return files; }
    public void setFiles(Object[] files) { this.files = files; }

    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
}
