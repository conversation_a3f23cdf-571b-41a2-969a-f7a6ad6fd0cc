package com.cool.modules.task.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class TaskPackageEntityTableDef extends TableDef {

    /**
     * 场景任务包实体
     */
    public static final TaskPackageEntityTableDef TASK_PACKAGE_ENTITY = new TaskPackageEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 标签（JSON格式）
     */
    public final QueryColumn TAGS = new QueryColumn(this, "tags");

    /**
     * 负责人ID
     */
    public final QueryColumn OWNER_ID = new QueryColumn(this, "owner_id");

    /**
     * 备注
     */
    public final QueryColumn REMARKS = new QueryColumn(this, "remarks");

    /**
     * 优先级：1-低，2-中，3-高，4-紧急
     */
    public final QueryColumn PRIORITY = new QueryColumn(this, "priority");

    /**
     * 创建人ID
     */
    public final QueryColumn CREATOR_ID = new QueryColumn(this, "creator_id");

    /**
     * 是否删除：0-否，1-是
     */
    public final QueryColumn IS_DELETED = new QueryColumn(this, "is_deleted");

    /**
     * 负责人姓名
     */
    public final QueryColumn OWNER_NAME = new QueryColumn(this, "owner_name");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    /**
     * 关联场景ID
     */
    public final QueryColumn SCENARIO_ID = new QueryColumn(this, "scenario_id");

    /**
     * 总任务数
     */
    public final QueryColumn TOTAL_TASKS = new QueryColumn(this, "total_tasks");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    /**
     * 创建人姓名
     */
    public final QueryColumn CREATOR_NAME = new QueryColumn(this, "creator_name");

    /**
     * 任务包描述
     */
    public final QueryColumn DESCRIPTION = new QueryColumn(this, "description");

    /**
     * 任务包名称
     */
    public final QueryColumn PACKAGE_NAME = new QueryColumn(this, "package_name");

    /**
     * 任务包类型：0-AI生成，1-手动创建，2-模板生成
     */
    public final QueryColumn PACKAGE_TYPE = new QueryColumn(this, "package_type");

    /**
     * 关联的工单ID
     */
    public final QueryColumn WORK_ORDER_ID = new QueryColumn(this, "work_order_id");

    /**
     * 所属部门ID
     */
    public final QueryColumn DEPARTMENT_ID = new QueryColumn(this, "department_id");

    /**
     * 待分配任务数
     */
    public final QueryColumn PENDING_TASKS = new QueryColumn(this, "pending_tasks");

    /**
     * 场景编码（冗余字段，便于查询）
     */
    public final QueryColumn SCENARIO_CODE = new QueryColumn(this, "scenario_code");

    /**
     * 场景名称（冗余字段，便于查询）
     */
    public final QueryColumn SCENARIO_NAME = new QueryColumn(this, "scenario_name");

    /**
     * 实际结束时间
     */
    public final QueryColumn ACTUAL_END_TIME = new QueryColumn(this, "actual_end_time");

    /**
     * 任务包状态：0-待分配，1-执行中，2-已完成，3-已关闭
     */
    public final QueryColumn PACKAGE_STATUS = new QueryColumn(this, "package_status");

    /**
     * 关联的SOP场景ID
     */
    public final QueryColumn SOP_SCENARIO_ID = new QueryColumn(this, "sop_scenario_id");

    /**
     * 已完成任务数
     */
    public final QueryColumn COMPLETED_TASKS = new QueryColumn(this, "completed_tasks");

    /**
     * 完成率（百分比）
     */
    public final QueryColumn COMPLETION_RATE = new QueryColumn(this, "completion_rate");

    /**
     * 实际开始时间
     */
    public final QueryColumn ACTUAL_START_TIME = new QueryColumn(this, "actual_start_time");

    /**
     * 预计结束时间
     */
    public final QueryColumn EXPECTED_END_TIME = new QueryColumn(this, "expected_end_time");

    /**
     * 进行中任务数
     */
    public final QueryColumn IN_PROGRESS_TASKS = new QueryColumn(this, "in_progress_tasks");

    /**
     * 预计开始时间
     */
    public final QueryColumn EXPECTED_START_TIME = new QueryColumn(this, "expected_start_time");

    /**
     * 创建者部门ID
     */
    public final QueryColumn CREATOR_DEPARTMENT_ID = new QueryColumn(this, "creator_department_id");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, TAGS, OWNER_ID, REMARKS, PRIORITY, CREATOR_ID, IS_DELETED, OWNER_NAME, CREATE_TIME, SCENARIO_ID, TOTAL_TASKS, UPDATE_TIME, CREATOR_NAME, DESCRIPTION, PACKAGE_NAME, PACKAGE_TYPE, WORK_ORDER_ID, DEPARTMENT_ID, PENDING_TASKS, SCENARIO_CODE, SCENARIO_NAME, ACTUAL_END_TIME, PACKAGE_STATUS, SOP_SCENARIO_ID, COMPLETED_TASKS, COMPLETION_RATE, ACTUAL_START_TIME, EXPECTED_END_TIME, IN_PROGRESS_TASKS, EXPECTED_START_TIME, CREATOR_DEPARTMENT_ID};

    public TaskPackageEntityTableDef() {
        super("", "task_package");
    }

    private TaskPackageEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public TaskPackageEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new TaskPackageEntityTableDef("", "task_package", alias));
    }

}
