package com.cool.modules.project.service.impl;

import com.cool.modules.project.entity.ProjectEntity;
import com.cool.modules.project.mapper.ProjectMapper;
import com.cool.modules.project.service.ProjectService;
import com.cool.core.base.BaseServiceImpl;
import com.mybatisflex.core.query.QueryWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 项目表Service实现
 */
@Service
public class ProjectServiceImpl extends BaseServiceImpl<ProjectMapper, ProjectEntity> implements ProjectService {

    @Override
    public int importProjects(List<ProjectEntity> projects) {
        int count = 0;
        for (ProjectEntity project : projects) {
            // 按code唯一索引查找
            ProjectEntity exist = getOne(QueryWrapper.create().eq(ProjectEntity::getCode, project.getCode()));
            if (exist != null) {
                project.setId(exist.getId());
                updateById(project);
            } else {
                save(project);
            }
            count++;
        }
        return count;
    }
} 