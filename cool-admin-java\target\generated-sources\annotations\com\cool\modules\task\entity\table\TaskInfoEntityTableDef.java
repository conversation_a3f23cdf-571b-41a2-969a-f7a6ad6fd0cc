package com.cool.modules.task.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class TaskInfoEntityTableDef extends TableDef {

    public static final TaskInfoEntityTableDef TASK_INFO_ENTITY = new TaskInfoEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn CRON = new QueryColumn(this, "cron");

    public final QueryColumn DATA = new QueryColumn(this, "data");

    public final QueryColumn NAME = new QueryColumn(this, "name");

    public final QueryColumn TYPE = new QueryColumn(this, "type");

    public final QueryColumn EVERY = new QueryColumn(this, "every");

    public final QueryColumn JOB_ID = new QueryColumn(this, "job_id");

    public final QueryColumn REMARK = new QueryColumn(this, "remark");

    public final QueryColumn STEP_ID = new QueryColumn(this, "step_id");

    public final QueryColumn END_TIME = new QueryColumn(this, "end_time");

    public final QueryColumn SERVICE = new QueryColumn(this, "service");

    public final QueryColumn CLOSED_BY = new QueryColumn(this, "closed_by");

    public final QueryColumn PRIORITY = new QueryColumn(this, "priority");

    public final QueryColumn STEP_CODE = new QueryColumn(this, "step_code");

    public final QueryColumn STEP_NAME = new QueryColumn(this, "step_name");

    public final QueryColumn CLOSE_TIME = new QueryColumn(this, "close_time");

    public final QueryColumn PACKAGE_ID = new QueryColumn(this, "package_id");

    public final QueryColumn START_TIME = new QueryColumn(this, "start_time");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn SCENARIO_ID = new QueryColumn(this, "scenario_id");

    public final QueryColumn TASK_STATUS = new QueryColumn(this, "task_status");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    public final QueryColumn CLOSE_REASON = new QueryColumn(this, "close_reason");

    public final QueryColumn DESCRIPTION = new QueryColumn(this, "description");

    public final QueryColumn NEXT_RUN_TIME = new QueryColumn(this, "next_run_time");

    public final QueryColumn REPEAT_COUNT = new QueryColumn(this, "repeat_count");

    /**
     * 所属部门ID
     */
    public final QueryColumn DEPARTMENT_ID = new QueryColumn(this, "department_id");

    public final QueryColumn EMPLOYEE_ROLE = new QueryColumn(this, "employee_role");

    public final QueryColumn SCENARIO_CODE = new QueryColumn(this, "scenario_code");

    public final QueryColumn SCENARIO_NAME = new QueryColumn(this, "scenario_name");

    public final QueryColumn SCHEDULE_TYPE = new QueryColumn(this, "schedule_type");

    public final QueryColumn TASK_ACTIVITY = new QueryColumn(this, "task_activity");

    public final QueryColumn TASK_CATEGORY = new QueryColumn(this, "task_category");

    public final QueryColumn PHOTO_REQUIRED = new QueryColumn(this, "photo_required");

    public final QueryColumn WORK_HIGHLIGHT = new QueryColumn(this, "work_highlight");

    public final QueryColumn COMPLETION_TIME = new QueryColumn(this, "completion_time");

    public final QueryColumn SCHEDULE_STATUS = new QueryColumn(this, "schedule_status");

    public final QueryColumn EMPLOYEE_BEHAVIOR = new QueryColumn(this, "employee_behavior");

    public final QueryColumn ENTITY_TOUCHPOINT = new QueryColumn(this, "entity_touchpoint");

    public final QueryColumn ATTACHMENT_REQUIRED = new QueryColumn(this, "attachment_required");

    /**
     * 创建者部门ID
     */
    public final QueryColumn CREATOR_DEPARTMENT_ID = new QueryColumn(this, "creator_department_id");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, CRON, DATA, NAME, TYPE, EVERY, JOB_ID, REMARK, STEP_ID, END_TIME, SERVICE, CLOSED_BY, PRIORITY, STEP_CODE, STEP_NAME, CLOSE_TIME, PACKAGE_ID, START_TIME, CREATE_TIME, SCENARIO_ID, TASK_STATUS, UPDATE_TIME, CLOSE_REASON, DESCRIPTION, NEXT_RUN_TIME, REPEAT_COUNT, DEPARTMENT_ID, EMPLOYEE_ROLE, SCENARIO_CODE, SCENARIO_NAME, SCHEDULE_TYPE, TASK_ACTIVITY, TASK_CATEGORY, PHOTO_REQUIRED, WORK_HIGHLIGHT, COMPLETION_TIME, SCHEDULE_STATUS, EMPLOYEE_BEHAVIOR, ENTITY_TOUCHPOINT, ATTACHMENT_REQUIRED, CREATOR_DEPARTMENT_ID};

    public TaskInfoEntityTableDef() {
        super("", "task_info");
    }

    private TaskInfoEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public TaskInfoEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new TaskInfoEntityTableDef("", "task_info", alias));
    }

}
