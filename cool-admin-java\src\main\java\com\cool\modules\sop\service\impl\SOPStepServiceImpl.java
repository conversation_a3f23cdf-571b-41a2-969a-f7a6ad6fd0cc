package com.cool.modules.sop.service.impl;

import com.cool.core.base.BaseServiceImpl;
import com.cool.modules.sop.entity.SOPStepEntity;
import com.cool.modules.sop.entity.SOPScenarioEntity;
import com.cool.modules.sop.mapper.SOPStepMapper;
import com.cool.modules.sop.service.SOPStepService;
import com.cool.modules.sop.service.SOPScenarioService;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * SOP步骤服务实现类
 */
@Slf4j
@Service
public class SOPStepServiceImpl extends BaseServiceImpl<SOPStepMapper, SOPStepEntity> implements SOPStepService {

    @Autowired
    private SOPScenarioService sopScenarioService;

    @Override
    public List<SOPStepEntity> getByTemplateId(Long templateId) {
        QueryWrapper wrapper = QueryWrapper.create()
            .eq("sop_id", templateId)
            .orderBy("step_order", true);
        return list(wrapper);
    }

    @Override
    @Transactional
    public void updateStepOrder(Long stepId, Integer newOrder) {
        SOPStepEntity step = getById(stepId);
        if (step == null) {
            throw new RuntimeException("步骤不存在");
        }
        
        step.setStepOrder(newOrder);
        step.setUpdateTime(new Date());
        updateById(step);
        
        log.info("步骤顺序已更新，ID: {}, 新顺序: {}", stepId, newOrder);
    }

    @Override
    @Transactional
    public void batchCreateSteps(Long templateId, List<SOPStepEntity> steps) {
        if (steps == null || steps.isEmpty()) {
            return;
        }
        
        for (SOPStepEntity step : steps) {
            step.setSopId(templateId);
            step.setCreateTime(new Date());
            save(step);
        }
        
        // 更新场景的总步骤数
        updateScenarioTotalSteps(templateId);
        
        log.info("批量创建步骤完成，模板ID: {}, 步骤数量: {}", templateId, steps.size());
    }

    @Override
    @Transactional
    public void deleteByTemplateId(Long templateId) {
        QueryWrapper wrapper = QueryWrapper.create().eq("sop_id", templateId);
        remove(wrapper);
        
        // 更新场景的总步骤数
        updateScenarioTotalSteps(templateId);
        
        log.info("已删除场景相关步骤，场景ID: {}", templateId);
    }

    /**
     * 更新场景的总步骤数
     */
    private void updateScenarioTotalSteps(Long scenarioId) {
        try {
            // 统计当前场景的步骤数
            QueryWrapper wrapper = QueryWrapper.create().eq("sop_id", scenarioId);
            long stepCount = count(wrapper);
            
            // 更新场景的总步骤数
            SOPScenarioEntity scenario = sopScenarioService.getById(scenarioId);
            if (scenario != null) {
                scenario.setTotalSteps((int) stepCount);
                sopScenarioService.updateById(scenario);
                log.info("场景 {} 总步骤数已更新为: {}", scenarioId, stepCount);
            }
        } catch (Exception e) {
            log.error("更新场景总步骤数失败，场景ID: {}, 错误: {}", scenarioId, e.getMessage());
        }
    }

    @Transactional
    public boolean saveBatch(List<SOPStepEntity> entityList) {
        boolean result = super.saveBatch(entityList);
        
        // 如果批量保存成功，更新相关场景的总步骤数
        if (result && !entityList.isEmpty()) {
            entityList.stream()
                .map(SOPStepEntity::getSopId)
                .distinct()
                .forEach(this::updateScenarioTotalSteps);
        }
        
        return result;
    }
} 