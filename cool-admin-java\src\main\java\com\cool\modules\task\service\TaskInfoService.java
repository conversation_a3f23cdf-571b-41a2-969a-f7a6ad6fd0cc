package com.cool.modules.task.service;

import com.cool.core.base.BaseService;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.entity.TaskLogEntity;
import com.cool.modules.task.entity.TaskExecutionEntity;
import com.mybatisflex.core.paginate.Page;
import java.util.List;

/**
 * 任务信息
 */
public interface TaskInfoService extends BaseService<TaskInfoEntity> {

    /**
     * 初始化任务
     */
    void init();

    /**
     * 执行一次
     *
     * @param taskId 任务ID
     */
    void once(Long taskId);

    /**
     * 停止任务
     *
     * @param taskId 任务ID
     */
    void stop(Long taskId);

    /**
     * 任务日志
     *
     * @param taskId 任务ID
     * @param status 任务状态
     * @return 日志列表
     */
    Object log(Page<TaskLogEntity> page, Long taskId, Integer status);

    /**
     * 开始任务
     *
     * @param taskId 任务ID
     * @param type   任务类型
     */
    void start(Long taskId, Integer type);

    /**
     * 获取任务执行记录
     *
     * @param taskId 任务ID
     * @return 执行记录列表
     */
    List<TaskExecutionEntity> getTaskExecutions(Long taskId);

    /**
     * 获取个人工作台任务分页（包含执行信息）
     *
     * @param assigneeId 执行人ID
     * @param page 页码
     * @param size 页大小
     * @param businessStatus 业务状态筛选
     * @return 任务分页数据
     */
    com.mybatisflex.core.paginate.Page<TaskInfoEntity> getPersonalTasksWithExecution(Long assigneeId, Integer page, Integer size, String businessStatus);

    /**
     * 开始任务
     *
     * @param taskId 任务ID
     * @param assigneeId 执行人ID
     * @return 是否成功
     */
    boolean startTask(Long taskId, Long assigneeId);

    /**
     * 批量更新任务时间
     *
     * @param taskIds 任务ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 是否成功
     */
    boolean batchUpdateTaskTime(List<Long> taskIds, String startTime, String endTime);

    /**
     * 批量通过ID查询任务信息
     * @param ids 任务ID列表
     * @return 任务信息列表
     */
    List<TaskInfoEntity> listByIds(List<Long> ids);
}
