package com.cool.modules.organization.integration;

import cn.hutool.core.lang.Dict;
import com.cool.modules.organization.dto.OrganizationModeSwitchDTO;
import com.cool.modules.organization.entity.ProjectInfoEntity;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import com.cool.modules.organization.service.DualDimensionMigrationService;
import com.cool.modules.organization.service.OrganizationModeService;
import com.cool.modules.organization.service.ProjectInfoService;
import com.cool.modules.task.entity.TaskPackageEntity;
import com.cool.modules.task.service.TaskPackageService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 双维度权限集成测试
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class DualDimensionIntegrationTest {
    
    @Autowired
    private OrganizationModeService organizationModeService;
    
    @Autowired
    private ProjectInfoService projectInfoService;
    
    @Autowired
    private TaskPackageService taskPackageService;
    
    @Autowired
    private DualDimensionMigrationService migrationService;
    
    private Long testUserId = 1L;
    private Long testDepartmentId = 1L;
    private Long testProjectId;
    
    @BeforeEach
    void setUp() {
        // 创建测试项目
        ProjectInfoEntity project = new ProjectInfoEntity();
        project.setProjectName("测试项目");
        project.setProjectCode("TEST_PROJECT_001");
        project.setDescription("集成测试项目");
        project.setOwnerId(testUserId);
        project.setCreatorId(testUserId);
        project.setCreateTime(new Date());
        project.setStatus(0);
        project.setPriority(2);
        project.setEnabled(true);
        
        projectInfoService.createProject(project);
        testProjectId = project.getId();
    }
    
    @Test
    void testOrganizationModeSwitching() {
        // 1. 验证初始状态（部门模式）
        String currentMode = organizationModeService.getCurrentMode(testUserId);
        assertEquals(OrganizationModeEnum.DEPARTMENT.getCode(), currentMode, 
                    "初始状态应该是部门模式");
        
        // 2. 切换到项目模式
        OrganizationModeSwitchDTO switchDTO = new OrganizationModeSwitchDTO();
        switchDTO.setUserId(testUserId);
        switchDTO.setTargetMode(OrganizationModeEnum.PROJECT.getCode());
        switchDTO.setReason("集成测试切换");
        
        boolean switchSuccess = organizationModeService.switchMode(switchDTO);
        assertTrue(switchSuccess, "切换到项目模式应该成功");
        
        // 3. 验证切换后的状态
        String newMode = organizationModeService.getCurrentMode(testUserId);
        assertEquals(OrganizationModeEnum.PROJECT.getCode(), newMode, 
                    "切换后应该是项目模式");
        
        // 4. 切换回部门模式
        switchDTO.setTargetMode(OrganizationModeEnum.DEPARTMENT.getCode());
        boolean switchBackSuccess = organizationModeService.switchMode(switchDTO);
        assertTrue(switchBackSuccess, "切换回部门模式应该成功");
        
        String finalMode = organizationModeService.getCurrentMode(testUserId);
        assertEquals(OrganizationModeEnum.DEPARTMENT.getCode(), finalMode, 
                    "最终应该回到部门模式");
    }
    
    @Test
    void testTaskPackageCreationInDifferentModes() {
        // 1. 在部门模式下创建任务包
        TaskPackageEntity departmentTask = new TaskPackageEntity();
        departmentTask.setPackageName("部门任务包");
        departmentTask.setDescription("部门模式下的任务包");
        departmentTask.setDepartmentId(testDepartmentId);
        departmentTask.setCreatorId(testUserId);
        departmentTask.setCreateTime(new Date());
        departmentTask.setPackageStatus(0);
        
        taskPackageService.save(departmentTask);
        assertNotNull(departmentTask.getId(), "部门任务包应该创建成功");
        assertNotNull(departmentTask.getDepartmentId(), "部门任务包应该有部门ID");
        assertNull(departmentTask.getProjectId(), "部门任务包不应该有项目ID");
        
        // 2. 切换到项目模式
        OrganizationModeSwitchDTO switchDTO = new OrganizationModeSwitchDTO();
        switchDTO.setUserId(testUserId);
        switchDTO.setTargetMode(OrganizationModeEnum.PROJECT.getCode());
        switchDTO.setReason("测试项目模式创建任务");
        
        organizationModeService.switchMode(switchDTO);
        
        // 3. 在项目模式下创建任务包
        TaskPackageEntity projectTask = new TaskPackageEntity();
        projectTask.setPackageName("项目任务包");
        projectTask.setDescription("项目模式下的任务包");
        projectTask.setProjectId(testProjectId);
        projectTask.setCreatorId(testUserId);
        projectTask.setCreateTime(new Date());
        projectTask.setPackageStatus(0);
        
        taskPackageService.save(projectTask);
        assertNotNull(projectTask.getId(), "项目任务包应该创建成功");
        assertNotNull(projectTask.getProjectId(), "项目任务包应该有项目ID");
        assertNull(projectTask.getDepartmentId(), "项目任务包不应该有部门ID");
    }
    
    @Test
    void testDataMigration() {
        // 1. 执行数据迁移
        DualDimensionMigrationService.MigrationResult result = migrationService.executeFullMigration();
        
        assertTrue(result.isSuccess(), "数据迁移应该成功");
        assertNotNull(result.getMessage(), "迁移结果应该有消息");
        assertTrue(result.getExecutionTime() > 0, "执行时间应该大于0");
        
        // 2. 验证数据一致性
        DualDimensionMigrationService.ValidationResult validation = migrationService.validateDataConsistency();
        
        assertTrue(validation.isValid(), "数据一致性验证应该通过");
        assertEquals(0, validation.getTaskPackageIssues(), "任务包数据应该没有问题");
        assertEquals(0, validation.getTaskInfoIssues(), "任务信息数据应该没有问题");
        assertEquals(0, validation.getTaskExecutionIssues(), "任务执行数据应该没有问题");
        assertEquals(0, validation.getWorkOrderIssues(), "工单数据应该没有问题");
        
        // 3. 验证迁移状态
        DualDimensionMigrationService.MigrationStatus status = migrationService.getMigrationStatus();
        assertEquals(DualDimensionMigrationService.MigrationStatus.COMPLETED, status, 
                    "迁移状态应该是已完成");
    }
    
    @Test
    void testPermissionScopeInDifferentModes() {
        // 1. 测试部门模式下的权限范围
        var departmentScope = organizationModeService.getUserPermissionScope(testUserId);
        
        assertNotNull(departmentScope, "部门权限范围不应该为空");
        assertEquals(OrganizationModeEnum.DEPARTMENT.getCode(), departmentScope.getCurrentMode(), 
                    "当前模式应该是部门模式");
        assertFalse(departmentScope.getDepartmentIds().isEmpty(), "应该有部门权限");
        
        // 2. 切换到项目模式
        OrganizationModeSwitchDTO switchDTO = new OrganizationModeSwitchDTO();
        switchDTO.setUserId(testUserId);
        switchDTO.setTargetMode(OrganizationModeEnum.PROJECT.getCode());
        switchDTO.setReason("测试权限范围");
        
        organizationModeService.switchMode(switchDTO);
        
        // 3. 测试项目模式下的权限范围
        var projectScope = organizationModeService.getUserPermissionScope(testUserId);
        
        assertNotNull(projectScope, "项目权限范围不应该为空");
        assertEquals(OrganizationModeEnum.PROJECT.getCode(), projectScope.getCurrentMode(), 
                    "当前模式应该是项目模式");
        // 注意：用户可能还没有项目权限，这是正常的
    }
    
    @Test
    void testProjectManagement() {
        // 1. 创建项目
        ProjectInfoEntity project = new ProjectInfoEntity();
        project.setProjectName("集成测试项目2");
        project.setProjectCode("TEST_PROJECT_002");
        project.setDescription("第二个集成测试项目");
        project.setOwnerId(testUserId);
        project.setStatus(0);
        project.setPriority(3);
        
        boolean createSuccess = projectInfoService.createProject(project);
        assertTrue(createSuccess, "项目创建应该成功");
        assertNotNull(project.getId(), "项目应该有ID");
        
        // 2. 获取项目详情
        ProjectInfoEntity projectDetail = projectInfoService.getProjectWithStats(project.getId());
        assertNotNull(projectDetail, "项目详情不应该为空");
        assertEquals(project.getProjectName(), projectDetail.getProjectName(), "项目名称应该一致");
        
        // 3. 检查项目访问权限
        boolean hasAccess = projectInfoService.hasProjectAccess(testUserId, project.getId());
        assertTrue(hasAccess, "项目创建者应该有访问权限");
        
        // 4. 更新项目状态
        boolean updateSuccess = projectInfoService.updateStatus(project.getId(), 1);
        assertTrue(updateSuccess, "项目状态更新应该成功");
    }
    
    @Test
    void testMigrationRollback() {
        // 1. 执行迁移
        DualDimensionMigrationService.MigrationResult result = migrationService.executeFullMigration();
        assertTrue(result.isSuccess(), "迁移应该成功");
        
        // 2. 执行回滚
        boolean rollbackSuccess = migrationService.rollbackMigration();
        assertTrue(rollbackSuccess, "回滚应该成功");
        
        // 3. 验证回滚状态
        DualDimensionMigrationService.MigrationStatus status = migrationService.getMigrationStatus();
        assertEquals(DualDimensionMigrationService.MigrationStatus.ROLLED_BACK, status, 
                    "迁移状态应该是已回滚");
    }
    
    @Test
    void testCanSwitchToMode() {
        // 测试用户是否可以切换到不同模式
        boolean canSwitchToDepartment = organizationModeService.canSwitchToMode(testUserId, 
                                                                               OrganizationModeEnum.DEPARTMENT.getCode());
        assertTrue(canSwitchToDepartment, "用户应该可以切换到部门模式");
        
        boolean canSwitchToProject = organizationModeService.canSwitchToMode(testUserId, 
                                                                            OrganizationModeEnum.PROJECT.getCode());
        // 注意：用户可能没有项目权限，这取决于具体的权限配置
        assertNotNull(canSwitchToProject, "切换权限检查应该返回结果");
    }
}
