package com.cool.modules.sop.service;

import com.cool.core.base.BaseService;
import com.cool.modules.sop.entity.SOPScenarioEntity;

import java.util.List;
import java.util.Map;

/**
 * SOP场景服务接口
 */
public interface SOPScenarioService extends BaseService<SOPScenarioEntity> {

    /**
     * 根据行业获取SOP场景
     */
    List<SOPScenarioEntity> getByIndustry(String industryCode);

    /**
     * 根据阶段获取SOP场景
     */
    List<SOPScenarioEntity> getByPhase(String phaseCode);

    /**
     * 复制SOP场景
     */
    SOPScenarioEntity copyScenario(Long id, String newScenarioName, String newScenarioCode);

    /**
     * 发布SOP场景
     */
    void publishScenario(Long id);

    /**
     * 归档SOP场景
     */
    void archiveScenario(Long id);

    /**
     * 更新版本
     */
    void updateVersion(Long id, String version);

    /**
     * AI转换执行周期
     */
    void convertExecutionCycleByAI(Long id);

    /**
     * 获取SOP场景统计
     */
    Map<String, Object> getScenarioStats(String industryCode);

    void generateTasksFromScenario(Long scenarioId);

    /**
     * 使用AI根据用户输入生成任务
     */
    Map<String, Object> generateTasksByAI(String userInput);

    String getScenariosAsJsonContext();

    /**
     * 根据用户输入获取智能场景建议
     */
    List<Map<String, Object>> getSmartScenarioSuggestions(String description);

    /**
     * 获取所有场景标签
     * @return 场景标签列表
     */
    List<Map<String, Object>> getAllScenarioTags();

    /**
     * 修复所有场景的总步骤数
     */
    void fixAllScenarioTotalSteps();
} 