package com.cool.modules.organization.service;

import com.cool.modules.organization.dto.DataPermissionScopeDTO;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import com.cool.modules.organization.service.impl.DualDimensionDataPermissionServiceImpl;
import com.cool.modules.organization.service.impl.OrganizationModeServiceImpl;
import com.mybatisflex.core.query.QueryWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * 双维度权限测试
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@ExtendWith(MockitoExtension.class)
public class DualDimensionPermissionTest {
    
    @Mock
    private OrganizationModeService organizationModeService;
    
    @InjectMocks
    private DualDimensionDataPermissionServiceImpl permissionService;
    
    private DataPermissionScopeDTO departmentScope;
    private DataPermissionScopeDTO projectScope;
    private DataPermissionScopeDTO adminScope;
    
    @BeforeEach
    void setUp() {
        // 部门权限范围
        departmentScope = new DataPermissionScopeDTO();
        departmentScope.setUserId(1L);
        departmentScope.setCurrentMode(OrganizationModeEnum.DEPARTMENT.getCode());
        departmentScope.setDepartmentIds(Arrays.asList(1L, 2L, 3L));
        departmentScope.setProjectIds(Arrays.asList());
        departmentScope.setIsSystemAdmin(false);
        departmentScope.setIsUnlimited(false);
        
        // 项目权限范围
        projectScope = new DataPermissionScopeDTO();
        projectScope.setUserId(2L);
        projectScope.setCurrentMode(OrganizationModeEnum.PROJECT.getCode());
        projectScope.setDepartmentIds(Arrays.asList());
        projectScope.setProjectIds(Arrays.asList(10L, 20L));
        projectScope.setIsSystemAdmin(false);
        projectScope.setIsUnlimited(false);
        
        // 管理员权限范围
        adminScope = new DataPermissionScopeDTO();
        adminScope.setUserId(999L);
        adminScope.setCurrentMode(OrganizationModeEnum.DEPARTMENT.getCode());
        adminScope.setDepartmentIds(Arrays.asList());
        adminScope.setProjectIds(Arrays.asList());
        adminScope.setIsSystemAdmin(true);
        adminScope.setIsUnlimited(true);
    }
    
    @Test
    void testDepartmentModePermissionFilter() {
        // 模拟部门模式下的权限范围
        when(organizationModeService.getUserPermissionScope(1L)).thenReturn(departmentScope);
        
        QueryWrapper queryWrapper = QueryWrapper.create();
        permissionService.applyDataPermissionFilter(queryWrapper, 1L, "TaskPackage");
        
        // 验证查询条件包含部门ID过滤
        String sql = queryWrapper.toSQL();
        assertTrue(sql.contains("department_id"), "应该包含部门ID过滤条件");
        assertFalse(sql.contains("project_id"), "不应该包含项目ID过滤条件");
    }
    
    @Test
    void testProjectModePermissionFilter() {
        // 模拟项目模式下的权限范围
        when(organizationModeService.getUserPermissionScope(2L)).thenReturn(projectScope);
        
        QueryWrapper queryWrapper = QueryWrapper.create();
        permissionService.applyDataPermissionFilter(queryWrapper, 2L, "TaskPackage");
        
        // 验证查询条件包含项目ID过滤
        String sql = queryWrapper.toSQL();
        assertTrue(sql.contains("project_id"), "应该包含项目ID过滤条件");
        assertFalse(sql.contains("department_id"), "不应该包含部门ID过滤条件");
    }
    
    @Test
    void testAdminUnlimitedPermission() {
        // 模拟管理员权限范围
        when(organizationModeService.getUserPermissionScope(999L)).thenReturn(adminScope);
        
        QueryWrapper queryWrapper = QueryWrapper.create();
        permissionService.applyDataPermissionFilter(queryWrapper, 999L, "TaskPackage");
        
        // 验证管理员不受权限限制
        String sql = queryWrapper.toSQL();
        assertFalse(sql.contains("department_id"), "管理员不应该有部门ID过滤");
        assertFalse(sql.contains("project_id"), "管理员不应该有项目ID过滤");
    }
    
    @Test
    void testEmptyPermissionScope() {
        // 模拟无权限的情况
        DataPermissionScopeDTO emptyScope = new DataPermissionScopeDTO();
        emptyScope.setUserId(3L);
        emptyScope.setCurrentMode(OrganizationModeEnum.DEPARTMENT.getCode());
        emptyScope.setDepartmentIds(Arrays.asList());
        emptyScope.setProjectIds(Arrays.asList());
        emptyScope.setIsSystemAdmin(false);
        emptyScope.setIsUnlimited(false);
        
        when(organizationModeService.getUserPermissionScope(3L)).thenReturn(emptyScope);
        
        QueryWrapper queryWrapper = QueryWrapper.create();
        permissionService.applyDataPermissionFilter(queryWrapper, 3L, "TaskPackage");
        
        // 验证无权限时返回空结果
        String sql = queryWrapper.toSQL();
        assertTrue(sql.contains("1 = 0") || sql.contains("1=0"), "无权限时应该返回空结果");
    }
    
    @Test
    void testIsSystemAdmin() {
        assertTrue(permissionService.isSystemAdmin(999L), "管理员用户应该返回true");
        assertFalse(permissionService.isSystemAdmin(1L), "普通用户应该返回false");
    }
    
    @Test
    void testHasPermissionInCurrentMode() {
        when(organizationModeService.getUserPermissionScope(1L)).thenReturn(departmentScope);
        when(organizationModeService.getUserPermissionScope(2L)).thenReturn(projectScope);
        
        // 测试部门模式权限
        assertTrue(permissionService.hasPermissionInCurrentMode(1L, Arrays.asList(1L, 2L, 3L)), 
                  "用户在部门模式下应该有权限");
        assertFalse(permissionService.hasPermissionInCurrentMode(1L, Arrays.asList(4L, 5L)), 
                   "用户在部门模式下对其他部门应该无权限");
        
        // 测试项目模式权限
        assertTrue(permissionService.hasPermissionInCurrentMode(2L, Arrays.asList(10L, 20L)), 
                  "用户在项目模式下应该有权限");
        assertFalse(permissionService.hasPermissionInCurrentMode(2L, Arrays.asList(30L, 40L)), 
                   "用户在项目模式下对其他项目应该无权限");
    }
    
    @Test
    void testGetUserPermissionScope() {
        when(organizationModeService.getUserPermissionScope(1L)).thenReturn(departmentScope);
        
        DataPermissionScopeDTO scope = permissionService.getUserPermissionScope(1L);
        
        assertNotNull(scope, "权限范围不应该为空");
        assertEquals(OrganizationModeEnum.DEPARTMENT.getCode(), scope.getCurrentMode(), 
                    "当前模式应该是部门模式");
        assertEquals(3, scope.getDepartmentIds().size(), "应该有3个部门权限");
        assertTrue(scope.getProjectIds().isEmpty(), "项目权限应该为空");
    }
    
    @Test
    void testEntityTypeMapping() {
        // 测试不同实体类型的权限过滤
        when(organizationModeService.getUserPermissionScope(anyLong())).thenReturn(departmentScope);
        
        String[] entityTypes = {"TaskPackage", "TaskInfo", "TaskExecution", "WorkOrder"};
        
        for (String entityType : entityTypes) {
            QueryWrapper queryWrapper = QueryWrapper.create();
            permissionService.applyDataPermissionFilter(queryWrapper, 1L, entityType);
            
            String sql = queryWrapper.toSQL();
            assertFalse(sql.isEmpty(), "实体类型 " + entityType + " 应该生成有效的SQL");
        }
    }
    
    @Test
    void testPermissionCaching() {
        // 测试权限缓存机制
        when(organizationModeService.getUserPermissionScope(1L)).thenReturn(departmentScope);
        
        // 多次调用应该使用缓存
        DataPermissionScopeDTO scope1 = permissionService.getUserPermissionScope(1L);
        DataPermissionScopeDTO scope2 = permissionService.getUserPermissionScope(1L);
        
        assertNotNull(scope1, "第一次获取权限范围不应该为空");
        assertNotNull(scope2, "第二次获取权限范围不应该为空");
        assertEquals(scope1.getCurrentMode(), scope2.getCurrentMode(), "缓存的权限范围应该一致");
    }
}
