import { ModuleConfig } from "/@/cool";

export default (): ModuleConfig => {
  return {
    enable: true,
    label: "项目管理",
    description: "项目管理，支持项目基础信息的增删改查和批量导入",
    author: "Cool Team",
    version: "1.0.0",
    updateTime: "2024-06-01",
    order: 81,
    views: [
      {
        path: "/project",
        name: "project",
        meta: {
          label: "项目管理",
          icon: "el-icon-office-building",
          keepAlive: true
        },
        component: () => import("./views/project.vue")
      }
    ]
  };
}; 