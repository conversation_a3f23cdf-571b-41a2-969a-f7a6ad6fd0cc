<template>
  <div class="ai-generator-page">
    <el-scrollbar>
      <ai-task-generator />
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import AiTaskGenerator from "../components/AITaskGenerator.vue";

defineOptions({
  name: "sop-ai-generator"
});
</script>

<style lang="scss" scoped>
.ai-generator-page {
  height: 100vh;
  min-height: 0;
  background: var(--el-bg-color-page);
  overflow: auto;
  position: relative;
}

:deep(.el-scrollbar) {
  height: 100%;
  min-height: 0;
}

:deep(.el-scrollbar__view) {
  min-height: 100%;
  display: block !important;
  align-items: unset !important;
}

/* 暗色主题适配 */
html.dark .ai-generator-page {
  background: var(--el-bg-color-page) !important;
}
</style>