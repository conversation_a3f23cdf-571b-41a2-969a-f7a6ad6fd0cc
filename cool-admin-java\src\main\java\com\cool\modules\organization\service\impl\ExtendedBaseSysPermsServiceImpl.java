package com.cool.modules.organization.service.impl;

import cn.hutool.core.lang.Dict;
import com.cool.modules.base.service.sys.impl.BaseSysPermsServiceImpl;
import com.cool.modules.organization.service.DualDimensionPermsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 扩展的权限服务实现，支持双维度权限
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor
public class ExtendedBaseSysPermsServiceImpl extends BaseSysPermsServiceImpl {
    
    private final DualDimensionPermsService dualDimensionPermsService;
    
    @Override
    public Dict permmenu(Long adminUserId) {
        try {
            // 使用双维度权限服务获取权限菜单
            return dualDimensionPermsService.permmenu(adminUserId);
        } catch (Exception e) {
            log.error("获取双维度权限菜单失败，回退到原始实现", e);
            // 如果双维度权限服务失败，回退到原始实现
            return super.permmenu(adminUserId);
        }
    }
}
